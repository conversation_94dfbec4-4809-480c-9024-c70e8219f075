{"name": "@vben/vite-config", "version": "5.3.0-beta.2", "private": true, "homepage": "https://github.com/vbenjs/vue-vben-admin", "bugs": "https://github.com/vbenjs/vue-vben-admin/issues", "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "internal/vite-config"}, "license": "MIT", "type": "module", "scripts": {"stub": "pnpm unbuild --stub"}, "files": ["dist"], "main": "./dist/index.mjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"types": "./src/index.ts", "default": "./dist/index.mjs"}}, "dependencies": {"@intlify/unplugin-vue-i18n": "^5.0.0", "@jspm/generator": "^2.3.1", "archiver": "^7.0.1", "cheerio": "1.0.0", "get-port": "^7.1.0", "html-minifier-terser": "^7.2.0", "nitropack": "^2.9.7", "resolve.exports": "^2.0.2", "vite-plugin-lib-inject-css": "^2.1.1", "vite-plugin-pwa": "^0.20.5", "vite-plugin-vue-devtools": "^7.4.5"}, "devDependencies": {"@types/archiver": "^6.0.2", "@types/html-minifier-terser": "^7.0.2", "@vben/node-utils": "workspace:*", "@vitejs/plugin-vue": "^5.1.4", "@vitejs/plugin-vue-jsx": "^4.0.1", "dayjs": "^1.11.13", "dotenv": "^16.4.5", "rollup": "^4.21.3", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.79.1", "vite": "^5.4.6", "vite-plugin-compression": "^0.5.1", "vite-plugin-dts": "4.2.1", "vite-plugin-html": "^3.2.2"}}