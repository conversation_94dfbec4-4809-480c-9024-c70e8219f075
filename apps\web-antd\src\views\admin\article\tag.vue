<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import { Card, Button, Form, Input, Select, Row, Col, message } from 'ant-design-vue';
import { Plus, RefreshCw } from 'lucide-vue-next';

// 导入组件
import TagTable from '#/components/article/tag-table.vue';
import TagForm from '#/components/article/tag-form.vue';

// stores
import { useArticleTagStore } from '#/store/article/tag';
import { useArticleCategoryStore } from '#/store/article/category';

// types
import type { ArticleTag, ArticleTagListParams } from '#/types';

defineOptions({
  name: 'ArticleTagManage',
});

// 使用store
const tagStore = useArticleTagStore();
const categoryStore = useArticleCategoryStore();

// 搜索表单
const searchForm = reactive<ArticleTagListParams>({
  page: 1,
  page_size: 10,
  name: '',
  first_letter: '',
  category_id: undefined,
  category_weight: '',
});

// 分类选项
const categoryOptions = ref<Array<{ label: string; value: number }>>([]);

// 首字母选项
const firstLetterOptions = [
  'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M',
  'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'
].map(letter => ({ label: letter, value: letter }));

// 新增/编辑弹窗相关
const formModalVisible = ref(false);
const editingRecord = ref<ArticleTag | null>(null);

// 处理搜索
async function handleSearch() {
  searchForm.page = 1; // 重置到第一页
  await loadData();
}

// 重置搜索
async function handleReset() {
  searchForm.name = '';
  searchForm.first_letter = '';
  searchForm.category_id = undefined;
  searchForm.category_weight = '';
  searchForm.page = 1;
  await loadData();
}

// 加载数据
async function loadData() {
  try {
    await tagStore.getArticleTagList(searchForm);
  } catch (error) {
    message.error('加载数据失败');
  }
}

// 加载分类列表
async function loadCategories() {
  try {
    await categoryStore.getArticleCategoryList({ page: 1, page_size: 100 });
    categoryOptions.value = categoryStore.categories.map(category => ({
      label: category.name,
      value: category.id!,
    }));
  } catch (error) {
    message.error('加载分类列表失败');
  }
}

// 刷新数据
async function handleRefresh() {
  try {
    await loadData();
    message.success('数据刷新成功');
  } catch (error) {
    message.error('刷新失败');
  }
}

// 处理添加按钮点击
function handleAdd() {
  editingRecord.value = null;
  formModalVisible.value = true;
}

// 处理编辑操作
function handleEdit(record: ArticleTag) {
  editingRecord.value = JSON.parse(JSON.stringify(record));
  formModalVisible.value = true;
}

// 处理删除操作
async function handleDelete(id: number) {
  try {
    await tagStore.deleteArticleTag(id);
    message.success('删除成功');
  } catch (error) {
    message.error('删除失败');
  }
}

// 处理分页变化
function handlePageChange(page: number, pageSize: number) {
  searchForm.page = page;
  searchForm.page_size = pageSize;
  loadData();
}

// 添加/编辑确认
async function handleFormConfirm(formData: any) {
  try {
    if (editingRecord.value && editingRecord.value.id) {
      // 编辑模式
      await tagStore.updateArticleTag({
        ...editingRecord.value,
        ...formData,
        id: editingRecord.value.id,
      });
      message.success('更新成功');
    } else {
      // 新增模式
      const newData = { ...formData };
      // 移除可能存在的空ID字段
      if (newData.id === '' || newData.id === undefined) {
        delete newData.id;
      }
      await tagStore.addArticleTag(newData);
      message.success('添加成功');
    }
    formModalVisible.value = false;
    editingRecord.value = null;
  } catch (error) {
    message.error(editingRecord.value?.id ? '更新失败' : '添加失败');
  }
}

// 取消表单
function handleFormCancel() {
  formModalVisible.value = false;
  editingRecord.value = null;
}

// 组件挂载时加载数据
onMounted(() => {
  loadData();
  loadCategories();
});
</script>

<template>
  <div class="p-6">
    <Card>
      <!-- 页面标题和操作按钮 -->
      <div class="mb-6 flex items-center justify-between">
        <div>
          <h2 class="text-xl font-semibold text-gray-900">标签管理</h2>
          <p class="mt-1 text-sm text-gray-500">管理文章标签信息</p>
        </div>
        <div class="flex items-center gap-3">
          <Button
            :loading="tagStore.loading"
            title="刷新数据"
            @click="handleRefresh"
          >
            <template #icon>
              <RefreshCw :size="16" />
            </template>
          </Button>
          <Button
            type="primary"
            title="新增标签"
            @click="handleAdd"
          >
            <template #icon>
              <Plus :size="16" />
            </template>
          </Button>
        </div>
      </div>

      <!-- 搜索表单 -->
      <div class="mb-6 rounded-lg bg-gray-50 p-4">
        <Form layout="inline">
          <Row :gutter="16" class="w-full">
            <Col :span="5">
              <Form.Item label="标签名称">
                <Input
                  v-model:value="searchForm.name"
                  placeholder="请输入标签名称"
                  allow-clear
                />
              </Form.Item>
            </Col>
            <Col :span="4">
              <Form.Item label="首字母">
                <Select
                  v-model:value="searchForm.first_letter"
                  placeholder="选择首字母"
                  allow-clear
                  :options="firstLetterOptions"
                />
              </Form.Item>
            </Col>
            <Col :span="5">
              <Form.Item label="所属分类">
                <Select
                  v-model:value="searchForm.category_id"
                  placeholder="请选择分类"
                  allow-clear
                  :options="categoryOptions"
                  show-search
                  :filter-option="(input: string, option: any) => 
                    option.label.toLowerCase().includes(input.toLowerCase())
                  "
                />
              </Form.Item>
            </Col>
            <Col :span="10">
              <Form.Item>
                <div class="flex gap-2">
                  <Button
                    type="primary"
                    :loading="tagStore.loading"
                    @click="handleSearch"
                  >
                    搜索
                  </Button>
                  <Button @click="handleReset">
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </div>

      <!-- 数据表格 -->
      <TagTable
        :tags="tagStore.tags"
        :loading="tagStore.loading"
        :total-records="tagStore.totalRecords"
        :current-page="searchForm.page || 1"
        :page-size="searchForm.page_size || 10"
        @edit="handleEdit"
        @delete="handleDelete"
        @page-change="handlePageChange"
      />
    </Card>

    <!-- 新增/编辑弹窗 -->
    <TagForm
      v-model:visible="formModalVisible"
      :editing-record="editingRecord"
      @cancel="handleFormCancel"
      @confirm="handleFormConfirm"
    />
  </div>
</template>
