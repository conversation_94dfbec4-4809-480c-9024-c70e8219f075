<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import { Card, Button, Form, Input, Select, Row, Col, message } from 'ant-design-vue';
import { Plus, RefreshCw } from 'lucide-vue-next';

// 导入组件
import CategoryTable from '#/components/article/category-table.vue';
import CategoryForm from '#/components/article/category-form.vue';

// stores
import { useArticleCategoryStore } from '#/store/article/category';

// types
import type { ArticleCategory, ArticleCategoryListParams } from '#/types';
import { CATEGORY_WEIGHT_OPTIONS } from '#/types';

defineOptions({
  name: 'ArticleCategoryManage',
});

// 使用store
const categoryStore = useArticleCategoryStore();

// 搜索表单
const searchForm = reactive<ArticleCategoryListParams>({
  page: 1,
  page_size: 10,
  name: '',
  weight: '',
});

// 新增/编辑弹窗相关
const formModalVisible = ref(false);
const editingRecord = ref<ArticleCategory | null>(null);

// 处理搜索
async function handleSearch() {
  searchForm.page = 1; // 重置到第一页
  await loadData();
}

// 重置搜索
async function handleReset() {
  searchForm.name = '';
  searchForm.weight = '';
  searchForm.page = 1;
  await loadData();
}

// 加载数据
async function loadData() {
  try {
    await categoryStore.getArticleCategoryList(searchForm);
  } catch (error) {
    message.error('加载数据失败');
  }
}

// 刷新数据
async function handleRefresh() {
  try {
    await loadData();
    message.success('数据刷新成功');
  } catch (error) {
    message.error('刷新失败');
  }
}

// 处理添加按钮点击
function handleAdd() {
  editingRecord.value = null;
  formModalVisible.value = true;
}

// 处理编辑操作
function handleEdit(record: ArticleCategory) {
  editingRecord.value = JSON.parse(JSON.stringify(record));
  formModalVisible.value = true;
}

// 处理删除操作
async function handleDelete(id: number) {
  try {
    await categoryStore.deleteArticleCategory(id);
    message.success('删除成功');
  } catch (error) {
    message.error('删除失败');
  }
}

// 处理分页变化
function handlePageChange(page: number, pageSize: number) {
  searchForm.page = page;
  searchForm.page_size = pageSize;
  loadData();
}

// 添加/编辑确认
async function handleFormConfirm(formData: any) {
  try {
    if (editingRecord.value && editingRecord.value.id) {
      // 编辑模式
      await categoryStore.updateArticleCategory({
        ...editingRecord.value,
        ...formData,
        id: editingRecord.value.id,
      });
      message.success('更新成功');
    } else {
      // 新增模式
      await categoryStore.addArticleCategory(formData);
      message.success('添加成功');
    }
    formModalVisible.value = false;
    editingRecord.value = null;
  } catch (error) {
    message.error(editingRecord.value?.id ? '更新失败' : '添加失败');
  }
}

// 取消表单
function handleFormCancel() {
  formModalVisible.value = false;
  editingRecord.value = null;
}

// 组件挂载时加载数据
onMounted(() => {
  loadData();
});
</script>

<template>
  <div class="p-6">
    <Card>
      <!-- 页面标题和操作按钮 -->
      <div class="mb-6 flex items-center justify-between">
        <div>
          <h2 class="text-xl font-semibold text-gray-900">标签分类管理</h2>
          <p class="mt-1 text-sm text-gray-500">管理文章标签的分类信息</p>
        </div>
        <div class="flex items-center gap-3">
          <Button
            :loading="categoryStore.loading"
            title="刷新数据"
            @click="handleRefresh"
          >
            <template #icon>
              <RefreshCw :size="16" />
            </template>
          </Button>
          <Button
            type="primary"
            title="新增分类"
            @click="handleAdd"
          >
            <template #icon>
              <Plus :size="16" />
            </template>
          </Button>
        </div>
      </div>

      <!-- 搜索表单 -->
      <div class="mb-6 rounded-lg bg-gray-50 p-4">
        <Form layout="inline">
          <Row :gutter="16" class="w-full">
            <Col :span="6">
              <Form.Item label="分类名称">
                <Input
                  v-model:value="searchForm.name"
                  placeholder="请输入分类名称"
                  allow-clear
                />
              </Form.Item>
            </Col>
            <Col :span="6">
              <Form.Item label="分类权重">
                <Select
                  v-model:value="searchForm.weight"
                  placeholder="请选择分类权重"
                  allow-clear
                  :options="CATEGORY_WEIGHT_OPTIONS"
                />
              </Form.Item>
            </Col>
            <Col :span="12">
              <Form.Item>
                <div class="flex gap-2">
                  <Button
                    type="primary"
                    :loading="categoryStore.loading"
                    @click="handleSearch"
                  >
                    搜索
                  </Button>
                  <Button @click="handleReset">
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </div>

      <!-- 数据表格 -->
      <CategoryTable
        :categories="categoryStore.categories"
        :loading="categoryStore.loading"
        :total-records="categoryStore.totalRecords"
        :current-page="searchForm.page || 1"
        :page-size="searchForm.page_size || 10"
        @edit="handleEdit"
        @delete="handleDelete"
        @page-change="handlePageChange"
      />
    </Card>

    <!-- 新增/编辑弹窗 -->
    <CategoryForm
      v-model:visible="formModalVisible"
      :editing-record="editingRecord"
      @cancel="handleFormCancel"
      @confirm="handleFormConfirm"
    />
  </div>
</template>
