<script setup lang="ts">
import '@wangeditor/editor/dist/css/style.css'; // 引入css
import { onBeforeUnmount, ref, shallowRef, watch, computed } from 'vue';
// @ts-ignore
import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
import type { IDomEditor, IEditorConfig, IToolbarConfig } from '@wangeditor/editor';

// 导入图片上传store
import { useImageStore } from '#/store/image/image';

defineOptions({
  name: 'RichEditor',
});

const props = defineProps<{
  modelValue: string;
  placeholder?: string;
  disabled?: boolean;
  height?: string | number;
}>();

const emit = defineEmits<{
  'update:modelValue': [value: string];
  change: [value: string];
}>();

// 使用图片store
const imageStore = useImageStore();

// 编辑器实例，必须用 shallowRef
const editorRef = shallowRef<IDomEditor>();

// 内容 HTML
const valueHtml = ref('');

// 工具栏配置
const toolbarConfig: Partial<IToolbarConfig> = {
  excludeKeys: [
    'group-video', // 排除视频
    'fullScreen', // 排除全屏
  ],
};

// 编辑器配置
const editorConfig: Partial<IEditorConfig> = {
  placeholder: props.placeholder || '请输入内容...',
  MENU_CONF: {
    // 配置上传图片
    uploadImage: {
      // 自定义上传
      async customUpload(file: File, insertFn: Function) {
        try {
          // 使用图片管理接口上传
          const imageInfo = await imageStore.uploadImage({
            file,
            original_name: file.name,
            category: 'article',
            is_public: true,
          });

          // 插入图片到编辑器
          insertFn(imageInfo.access_url, imageInfo.original_name, imageInfo.access_url);
        } catch (error) {
          console.error('图片上传失败:', error);
          // 上传失败时使用本地预览
          const url = URL.createObjectURL(file);
          insertFn(url, file.name, url);
        }
      },
    },
    // 配置上传视频
    uploadVideo: {
      // 自定义上传
      async customUpload(file: File, insertFn: Function) {
        // 这里可以实现视频上传逻辑
        // 暂时使用本地预览
        const url = URL.createObjectURL(file);
        insertFn(url);
      },
    },
  },
};

// 监听props变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue !== valueHtml.value) {
      valueHtml.value = newValue;
    }
  },
  { immediate: true }
);

// 监听disabled变化
watch(
  () => props.disabled,
  (disabled) => {
    if (editorRef.value) {
      if (disabled) {
        editorRef.value.disable();
      } else {
        editorRef.value.enable();
      }
    }
  }
);

// 处理创建编辑器
const handleCreated = (editor: IDomEditor) => {
  editorRef.value = editor;
  
  // 设置初始状态
  if (props.disabled) {
    editor.disable();
  }
};

// 处理内容变化
const handleChange = (editor: IDomEditor) => {
  const html = editor.getHtml();
  valueHtml.value = html;
  emit('update:modelValue', html);
  emit('change', html);
};

// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
  const editor = editorRef.value;
  if (editor == null) return;
  editor.destroy();
});

// 计算编辑器高度
const editorHeight = computed(() => {
  if (typeof props.height === 'number') {
    return `${props.height}px`;
  }
  return props.height || '400px';
});
</script>

<template>
  <div class="rich-editor">
    <Toolbar
      :editor="editorRef"
      :defaultConfig="toolbarConfig"
      :mode="'default'"
      class="border-b border-gray-200"
    />
    <Editor
      v-model="valueHtml"
      :defaultConfig="editorConfig"
      :mode="'default'"
      :style="{ height: editorHeight }"
      class="border border-gray-200 border-t-0"
      @onCreated="handleCreated"
      @onChange="handleChange"
    />
  </div>
</template>

<style scoped>
.rich-editor {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
}

.rich-editor:focus-within {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}
</style>

<style>
/* 编辑器样式 */
.w-e-text-placeholder {
  color: #bfbfbf !important;
}

.w-e-text-container {
  background-color: #fff !important;
}

.w-e-text-container [data-slate-editor] {
  padding: 15px !important;
  line-height: 1.6 !important;
}

.w-e-toolbar {
  background-color: #fafafa !important;
  border-bottom: 1px solid #d9d9d9 !important;
}

.w-e-toolbar .w-e-bar-item button {
  color: #595959 !important;
}

.w-e-toolbar .w-e-bar-item button:hover {
  color: #1890ff !important;
  background-color: #e6f7ff !important;
}

.w-e-toolbar .w-e-bar-item.w-e-bar-item-active button {
  color: #1890ff !important;
  background-color: #e6f7ff !important;
}
</style>
