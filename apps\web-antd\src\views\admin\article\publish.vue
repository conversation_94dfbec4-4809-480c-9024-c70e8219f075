<script setup lang="ts">
import { ref } from 'vue';
import { Card, message } from 'ant-design-vue';
import { useRouter } from 'vue-router';

// 导入组件
import ArticleForm from '#/components/article/article-form.vue';

// 导入store
import { useArticleStore } from '#/store/article/article';

// 导入类型
import type { ArticleFormData } from '#/types';

defineOptions({
  name: 'ArticlePublish',
});

const router = useRouter();
const articleStore = useArticleStore();

// 表单引用
const articleFormRef = ref();

// 处理文章发布
async function handleSubmit(formData: ArticleFormData) {
  try {
    await articleStore.addArticle(formData);
    message.success('文章发布成功！');

    // 重置表单
    articleFormRef.value?.resetForm();

    // 可选：跳转到文章列表页面
    // router.push('/article-manage/list');
  } catch (error) {
    message.error('文章发布失败，请重试');
    console.error('发布失败:', error);
  }
}

// 处理取消
function handleCancel() {
  // 可以跳转到文章列表或其他页面
  router.push('/article-manage/list');
}
</script>

<template>
  <div class="p-6">
    <Card>
      <!-- 页面标题 -->
      <div class="mb-6">
        <h2 class="text-xl font-semibold text-gray-900">文章发布</h2>
        <p class="mt-1 text-sm text-gray-500">创建和发布新的文章内容</p>
      </div>

      <!-- 文章发布表单 -->
      <ArticleForm
        ref="articleFormRef"
        :loading="articleStore.loading"
        @submit="handleSubmit"
        @cancel="handleCancel"
      />
    </Card>
  </div>
</template>
