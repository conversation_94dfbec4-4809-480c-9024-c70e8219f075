export type * from './IOSS';
export type * from './ISystem';
export type * from './IUser';
export type * from './article';
export * from './article';
export * from './image';

// 定义链接组项的接口
export interface LinkGroupItem {
  name: string;
  url: string;
}

// 网站配置管理相关类型
export interface SiteConfig {
  id?: number;
  title: string;
  link_url: string;
  prompt_text: string;
  logo?: string;
  is_enabled?: boolean;
  is_fixed_position?: boolean;
  is_new_page?: boolean;
  publish_time?: string;
  offline_time?: string;
  partner_code: string;
  is_hover_enabled?: boolean;
  create_time?: string;
  update_time?: string;
  creator_id?: number;
  updater_id?: number;
  link_groups?: LinkGroupItem[];
  statusLoading?: boolean; // 用于UI状态管理
}

export interface SiteConfigListParams {
  page?: number;
  page_size?: number;
  is_enabled?: boolean;
  partner_code?: string;
}

export interface SiteConfigFormData {
  title: string;
  link_url: string;
  prompt_text: string;
  logo?: string;
  is_enabled?: boolean;
  is_fixed_position?: boolean;
  is_new_page?: boolean;
  publish_time?: string;
  offline_time?: string;
  partner_code: string;
  is_hover_enabled?: boolean;
  link_groups?: LinkGroupItem[];
}

