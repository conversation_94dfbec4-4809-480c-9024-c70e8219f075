<script setup lang="ts">
import { ref, computed } from 'vue';
import { 
  Modal, 
  Form, 
  Input, 
  Button, 
  Space, 
  Alert,
  Spin,
  message 
} from 'ant-design-vue';
import { LinkIcon, Download } from 'lucide-vue-next';

// 导入store
import { useWechatCrawlerStore } from '#/store/article/wechat-crawler';
import type { WechatArticleInfo } from '#/store/article/wechat-crawler';

defineOptions({
  name: 'WechatCrawlerModal',
});

const props = defineProps<{
  visible: boolean;
}>();

const emit = defineEmits<{
  'update:visible': [visible: boolean];
  'crawl-success': [data: WechatArticleInfo];
}>();

// 使用store
const crawlerStore = useWechatCrawlerStore();

// 表单数据
const formData = ref({
  url: '',
});

// 表单引用
const formRef = ref();

// 表单验证规则
const formRules = {
  url: [
    { required: true, message: '请输入微信文章链接', trigger: 'blur' },
    { 
      validator: (_rule: any, value: string) => {
        if (!value) return Promise.resolve();
        if (!crawlerStore.isValidWechatUrl(value)) {
          return Promise.reject(new Error('请输入有效的微信公众号文章链接'));
        }
        return Promise.resolve();
      }, 
      trigger: 'blur' 
    },
  ],
};

// 计算属性
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
});

// 示例链接
const exampleUrls = [
  'https://mp.weixin.qq.com/s/xxxxxxxxxx',
  'https://mp.weixin.qq.com/s?__biz=xxx&mid=xxx&idx=1&sn=xxx',
];

// 处理爬取
async function handleCrawl() {
  try {
    await formRef.value?.validate();
    
    const articleData = await crawlerStore.crawlArticle(formData.value.url);
    
    message.success('文章爬取成功！');
    emit('crawl-success', articleData);
    handleCancel();
  } catch (error: any) {
    console.error('爬取失败:', error);
    message.error(error.message || '文章爬取失败，请检查链接是否正确');
  }
}

// 处理取消
function handleCancel() {
  modalVisible.value = false;
  formData.value.url = '';
  formRef.value?.resetFields();
}

// 填入示例链接
function fillExampleUrl(url: string) {
  formData.value.url = url;
}

// 处理URL输入变化
function handleUrlChange() {
  // 自动清理URL中的多余参数
  if (formData.value.url) {
    const url = formData.value.url.trim();
    if (crawlerStore.isValidWechatUrl(url)) {
      // 可以在这里做一些URL格式化处理
      formData.value.url = url;
    }
  }
}
</script>

<template>
  <Modal
    v-model:open="modalVisible"
    title="微信文章爬取"
    width="600px"
    :confirm-loading="crawlerStore.loading"
    @cancel="handleCancel"
  >
    <div class="wechat-crawler-modal">
      <!-- 使用说明 -->
      <Alert
        message="使用说明"
        description="请输入微信公众号文章的完整链接，系统将自动提取文章标题、内容等信息并填入编辑器。"
        type="info"
        show-icon
        class="mb-4"
      />

      <!-- 表单 -->
      <Form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <Form.Item label="微信文章链接" name="url">
          <Input
            v-model:value="formData.url"
            placeholder="请输入微信公众号文章链接"
            :prefix="LinkIcon"
            size="large"
            @change="handleUrlChange"
          >
            <template #suffix>
              <Button
                v-if="formData.url && crawlerStore.isValidWechatUrl(formData.url)"
                type="text"
                size="small"
                :loading="crawlerStore.loading"
                @click="handleCrawl"
              >
                <Download :size="16" />
              </Button>
            </template>
          </Input>
        </Form.Item>
      </Form>

      <!-- 示例链接 -->
      <div class="mb-4">
        <div class="text-sm text-gray-600 mb-2">链接格式示例：</div>
        <div class="space-y-1">
          <div
            v-for="(url, index) in exampleUrls"
            :key="index"
            class="text-xs text-gray-500 bg-gray-50 p-2 rounded cursor-pointer hover:bg-gray-100"
            @click="fillExampleUrl(url)"
          >
            {{ url }}
          </div>
        </div>
      </div>

      <!-- 注意事项 -->
      <Alert
        message="注意事项"
        type="warning"
        show-icon
        class="mb-4"
      >
        <template #description>
          <ul class="text-sm space-y-1">
            <li>• 仅支持微信公众号文章链接（mp.weixin.qq.com）</li>
            <li>• 爬取的内容会自动清理格式，保留基本样式</li>
            <li>• 如果爬取失败，请检查链接是否有效或网络连接</li>
            <li>• 请遵守相关法律法规，仅爬取有权限的内容</li>
          </ul>
        </template>
      </Alert>

      <!-- 加载状态 -->
      <div v-if="crawlerStore.loading" class="text-center py-4">
        <Spin size="large" />
        <div class="mt-2 text-gray-600">正在爬取文章内容，请稍候...</div>
      </div>
    </div>

    <template #footer>
      <Space>
        <Button @click="handleCancel">取消</Button>
        <Button
          type="primary"
          :loading="crawlerStore.loading"
          :disabled="!formData.url || !crawlerStore.isValidWechatUrl(formData.url)"
          @click="handleCrawl"
        >
          开始爬取
        </Button>
      </Space>
    </template>
  </Modal>
</template>

<style scoped>
.wechat-crawler-modal {
  max-height: 60vh;
  overflow-y: auto;
}

.example-url {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  background: #f5f5f5;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
  cursor: pointer;
  transition: all 0.2s;
}

.example-url:hover {
  background: #e6f7ff;
  border-color: #91d5ff;
}

:deep(.ant-alert-description ul) {
  margin: 0;
  padding-left: 0;
  list-style: none;
}

:deep(.ant-input-affix-wrapper-lg) {
  padding: 8px 12px;
}
</style>
