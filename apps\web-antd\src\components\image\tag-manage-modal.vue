<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { 
  Modal, 
  Form, 
  Input, 
  Button, 
  Table, 
  Space,
  Popconfirm,
  message 
} from 'ant-design-vue';
import { Plus, Edit, Trash2 } from 'lucide-vue-next';

// 导入store
import { useImageStore } from '#/store/image/image';

// 导入类型
import type { ImageTag, TagCreateParams } from '#/types/image';

defineOptions({
  name: 'TagManageModal',
});

const props = defineProps<{
  visible: boolean;
}>();

const emit = defineEmits<{
  'update:visible': [visible: boolean];
  'tag-created': [tag: ImageTag];
}>();

const imageStore = useImageStore();

// 组件状态
const loading = ref(false);
const showForm = ref(false);
const editingTag = ref<ImageTag | null>(null);

// 表单数据
const formData = ref<TagCreateParams>({
  name: '',
  description: '',
});

// 表单引用
const formRef = ref();

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入标签名称', trigger: 'blur' },
    { max: 20, message: '标签名称不能超过20个字符', trigger: 'blur' },
  ],
  description: [
    { max: 100, message: '标签描述不能超过100个字符', trigger: 'blur' },
  ],
};

// 计算属性
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
});

// 表格列定义
const columns = [
  {
    title: '标签名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '标签描述',
    dataIndex: 'description',
    key: 'description',
    ellipsis: true,
  },
  {
    title: '图片数量',
    dataIndex: 'image_count',
    key: 'image_count',
    width: 100,
    align: 'center' as const,
  },
  {
    title: '创建时间',
    dataIndex: 'create_time',
    key: 'create_time',
    width: 180,
  },
  {
    title: '操作',
    key: 'action',
    width: 120,
    align: 'center' as const,
  },
];

// 加载标签列表
async function loadTags() {
  try {
    loading.value = true;
    await imageStore.getTagList({ page_size: 100 });
  } catch (error) {
    message.error('加载标签列表失败');
  } finally {
    loading.value = false;
  }
}

// 显示新增表单
function showAddForm() {
  editingTag.value = null;
  formData.value = { name: '', description: '' };
  showForm.value = true;
}

// 显示编辑表单
function showEditForm(tag: ImageTag) {
  editingTag.value = tag;
  formData.value = {
    name: tag.name,
    description: tag.description || '',
  };
  showForm.value = true;
}

// 提交表单
async function handleSubmit() {
  try {
    await formRef.value?.validate();
    
    if (editingTag.value) {
      // 编辑标签
      await imageStore.updateTag(editingTag.value.id, formData.value);
      message.success('标签更新成功');
    } else {
      // 新增标签
      const newTag = await imageStore.createTag(formData.value);
      message.success('标签创建成功');
      emit('tag-created', newTag);
    }
    
    showForm.value = false;
    await loadTags();
  } catch (error) {
    if (error?.errorFields) {
      // 表单验证错误，不显示消息
      return;
    }
    message.error(editingTag.value ? '标签更新失败' : '标签创建失败');
  }
}

// 取消表单
function handleCancel() {
  showForm.value = false;
  formRef.value?.resetFields();
}

// 删除标签
async function handleDelete(id: number) {
  try {
    await imageStore.deleteTag(id);
    message.success('标签删除成功');
    await loadTags();
  } catch (error) {
    message.error('标签删除失败');
  }
}

// 关闭弹窗
function handleClose() {
  modalVisible.value = false;
  showForm.value = false;
}

// 组件挂载时加载数据
onMounted(() => {
  if (props.visible) {
    loadTags();
  }
});

// 监听弹窗显示状态
watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      loadTags();
    }
  }
);
</script>

<template>
  <Modal
    v-model:open="modalVisible"
    title="标签管理"
    width="800px"
    :footer="null"
    @cancel="handleClose"
  >
    <!-- 操作栏 -->
    <div class="mb-4 flex justify-between items-center">
      <h3 class="text-lg font-medium">标签列表</h3>
      <Button type="primary" @click="showAddForm">
       
        新增标签
      </Button>
    </div>

    <!-- 标签表格 -->
    <Table
      :columns="columns"
      :data-source="imageStore.tags"
      :loading="loading"
      row-key="id"
      :pagination="false"
      size="small"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'image_count'">
          <span>{{ record.image_count || 0 }}</span>
        </template>
        
        <template v-if="column.key === 'action'">
          <Space size="small">
            <Button
              type="text"
              size="small"
              @click="showEditForm(record)"
            >
              <template #icon>
                <Edit :size="14" />
              </template>
            </Button>
            
            <Popconfirm
              title="确定要删除这个标签吗？"
              @confirm="handleDelete(record.id)"
            >
              <Button
                type="text"
                danger
                size="small"
              >
                <template #icon>
                  <Trash2 :size="14" />
                </template>
              </Button>
            </Popconfirm>
          </Space>
        </template>
      </template>
    </Table>

    <!-- 新增/编辑表单弹窗 -->
    <Modal
      v-model:open="showForm"
      :title="editingTag ? '编辑标签' : '新增标签'"
      width="500px"
      @cancel="handleCancel"
    >
      <Form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <Form.Item label="标签名称" name="name">
          <Input
            v-model:value="formData.name"
            placeholder="请输入标签名称"
            :maxlength="20"
            show-count
          />
        </Form.Item>
        
        <Form.Item label="标签描述" name="description">
          <Input.TextArea
            v-model:value="formData.description"
            placeholder="请输入标签描述（可选）"
            :maxlength="100"
            :rows="3"
            show-count
          />
        </Form.Item>
      </Form>
      
      <template #footer>
        <Space>
          <Button @click="handleCancel">取消</Button>
          <Button type="primary" @click="handleSubmit">
            {{ editingTag ? '更新' : '创建' }}
          </Button>
        </Space>
      </template>
    </Modal>
  </Modal>
</template>

<style scoped>
:deep(.ant-table-tbody > tr > td) {
  padding: 8px 16px;
}

:deep(.ant-table-thead > tr > th) {
  padding: 12px 16px;
  font-weight: 600;
}
</style>
