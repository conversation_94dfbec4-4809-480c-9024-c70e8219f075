<script setup lang="ts">
import { computed } from 'vue';
import { 
  Table, 
  Button, 
  Tag, 
  Switch, 
  InputNumber, 
  Image, 
  Avatar,
  Tooltip,
  Space,
  message 
} from 'ant-design-vue';
import { 
  Edit, 
  MessageCircle, 
  Trash2, 
  Eye,
  EyeOff 
} from 'lucide-vue-next';
import dayjs from 'dayjs';

// 导入类型
import type { Article } from '#/types';
import { ARTICLE_STATUS_OPTIONS } from '#/types';

defineOptions({
  name: 'ArticleTable',
});

const props = defineProps<{
  articles: Article[];
  loading?: boolean;
  totalRecords?: number;
  currentPage?: number;
  pageSize?: number;
}>();

const emit = defineEmits<{
  edit: [record: Article];
  delete: [record: Article];
  comment: [record: Article];
  'page-change': [page: number, pageSize: number];
  'visibility-change': [record: Article, visible: boolean];
  'view-count-change': [record: Article, count: number];
}>();

// 表格列定义
const columns = computed(() => [
  {
    title: '封面',
    dataIndex: 'cover_image',
    key: 'cover_image',
    width: 120,
    align: 'center' as const,
  },
  {
    title: '标题',
    dataIndex: 'title',
    key: 'title',
    width: 200,
    ellipsis: true,
  },
  {
    title: '作者',
    dataIndex: 'author',
    key: 'author',
    width: 120,
  },
  {
    title: '标签',
    dataIndex: 'tags',
    key: 'tags',
    width: 150,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
    align: 'center' as const,
  },
  {
    title: '是否展示',
    dataIndex: 'is_visible',
    key: 'is_visible',
    width: 100,
    align: 'center' as const,
  },
  {
    title: '阅读数',
    dataIndex: 'view_count',
    key: 'view_count',
    width: 100,
    align: 'center' as const,
  },
  {
    title: '收藏数',
    dataIndex: 'favorite_count',
    key: 'favorite_count',
    width: 80,
    align: 'center' as const,
  },
  {
    title: '点赞数',
    dataIndex: 'like_count',
    key: 'like_count',
    width: 80,
    align: 'center' as const,
  },
  {
    title: '评论数',
    dataIndex: 'comment_count',
    key: 'comment_count',
    width: 80,
    align: 'center' as const,
  },
  {
    title: '发表时间',
    dataIndex: 'publish_time',
    key: 'publish_time',
    width: 150,
    sorter: true,
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    align: 'center' as const,
    fixed: 'right' as const,
  },
]);

// 获取状态标签配置
function getStatusTag(status: string) {
  const option = ARTICLE_STATUS_OPTIONS.find(item => item.value === status);
  const statusMap: Record<string, { color: string; text: string }> = {
    '0': { color: 'default', text: '草稿' },
    '1': { color: 'processing', text: '待审核' },
    '2': { color: 'warning', text: '审核中' },
    '3': { color: 'success', text: '已发布' },
  };
  return statusMap[status] || { color: 'default', text: option?.label || '未知' };
}

// 处理分页变化
function handlePageChange(page: number, pageSize: number) {
  emit('page-change', page, pageSize);
}

// 处理编辑
function handleEdit(record: Article) {
  emit('edit', record);
}

// 处理删除
function handleDelete(record: Article) {
  emit('delete', record);
}

// 处理查看评论
function handleComment(record: Article) {
  emit('comment', record);
}

// 处理可见性切换
function handleVisibilityChange(record: Article, checked: boolean) {
  emit('visibility-change', record, checked);
}

// 处理阅读数修改
function handleViewCountChange(record: Article, value: number | null) {
  if (value !== null && value >= 0) {
    emit('view-count-change', record, value);
  }
}

// 格式化时间
function formatTime(time: string) {
  return dayjs(time).format('YYYY-MM-DD HH:mm');
}

// 获取头像文本
function getAvatarText(name: string) {
  return name ? name.charAt(0).toUpperCase() : 'U';
}

// 获取头像颜色
function getAvatarColor(name: string) {
  const colors = [
    '#f56a00', '#7265e6', '#ffbf00', '#00a2ae', 
    '#87d068', '#108ee9', '#f50', '#2db7f5'
  ];
  let hash = 0;
  for (let i = 0; i < name.length; i++) {
    hash = name.charCodeAt(i) + ((hash << 5) - hash);
  }
  return colors[Math.abs(hash) % colors.length];
}
</script>

<template>
  <Table
    :columns="columns"
    :data-source="articles"
    :loading="loading"
    :pagination="{
      current: currentPage,
      pageSize: pageSize,
      total: totalRecords,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total: number, range: [number, number]) =>
        `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
      pageSizeOptions: ['10', '20', '50', '100'],
    }"
    row-key="id"
    :scroll="{ x: 1440 }"
    @change="(pagination: any) => handlePageChange(pagination.current, pagination.pageSize)"
  >
    <template #bodyCell="{ column, record }">
      <!-- 封面图 -->
      <template v-if="column.key === 'cover_image'">
        <div class="flex justify-center">
          <Image
            v-if="record.cover_image"
            :src="record.cover_image"
            :width="100"
            :height="70"
            :preview="false"
            class="rounded object-cover"
            fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
          />
          <div v-else class="w-[100px] h-[70px] bg-gray-100 rounded flex items-center justify-center">
            <span class="text-sm text-gray-400">无图</span>
          </div>
        </div>
      </template>

      <!-- 标题 -->
      <template v-if="column.key === 'title'">
        <Tooltip :title="record.title">
          <div class="font-medium text-gray-900 cursor-pointer hover:text-blue-600">
            {{ record.title }}
          </div>
        </Tooltip>
      </template>

      <!-- 作者 -->
      <template v-if="column.key === 'author'">
        <div class="flex items-center gap-2">
          <Avatar 
            :style="{ backgroundColor: getAvatarColor(record.author?.author_name || '') }"
            :size="24"
          >
            {{ getAvatarText(record.author?.author_name || '') }}
          </Avatar>
          <div class="text-sm">
            <div class="font-medium">{{ record.author?.author_name }}</div>
            <div class="text-gray-500 text-xs">{{ record.author?.wechat_nickname }}</div>
          </div>
        </div>
      </template>

      <!-- 标签 -->
      <template v-if="column.key === 'tags'">
        <div class="flex flex-wrap gap-1">
          <Tag
            v-for="tag in record.tags?.slice(0, 2)"
            :key="tag.id"
            size="small"
            color="blue"
          >
            {{ tag.name }}
          </Tag>
          <Tag v-if="(record.tags?.length || 0) > 2" size="small" color="default">
            +{{ (record.tags?.length || 0) - 2 }}
          </Tag>
        </div>
      </template>

      <!-- 状态 -->
      <template v-if="column.key === 'status'">
        <Tag :color="getStatusTag(record.status).color">
          {{ getStatusTag(record.status).text }}
        </Tag>
      </template>

      <!-- 是否展示 -->
      <template v-if="column.key === 'is_visible'">
        <Switch
          :checked="record.is_visible"
          checked-children="显示"
          un-checked-children="隐藏"
          @change="(checked: boolean) => handleVisibilityChange(record, checked)"
        />
      </template>

      <!-- 阅读数 -->
      <template v-if="column.key === 'view_count'">
        <InputNumber
          :value="record.view_count"
          :min="0"
          :max="999999999"
          size="small"
          class="w-20"
          @change="(value: number | null) => handleViewCountChange(record, value)"
        />
      </template>

      <!-- 收藏数 -->
      <template v-if="column.key === 'favorite_count'">
        <span class="text-gray-600">{{ record.favorite_count || 0 }}</span>
      </template>

      <!-- 点赞数 -->
      <template v-if="column.key === 'like_count'">
        <span class="text-gray-600">{{ record.like_count || 0 }}</span>
      </template>

      <!-- 评论数 -->
      <template v-if="column.key === 'comment_count'">
        <span class="text-gray-600">{{ record.comment_count || 0 }}</span>
      </template>

      <!-- 发表时间 -->
      <template v-if="column.key === 'publish_time'">
        <span class="text-sm text-gray-600">{{ formatTime(record.publish_time) }}</span>
      </template>

      <!-- 操作列 -->
      <template v-if="column.key === 'action'">
        <Space size="small">
          <Button
            type="text"
            size="small"
            title="编辑文章"
            @click="handleEdit(record)"
          >
            <template #icon>
              <Edit :size="14" />
            </template>
          </Button>

          <Button
            type="text"
            size="small"
            title="查看评论"
            @click="handleComment(record)"
          >
            <template #icon>
              <MessageCircle :size="14" />
            </template>
          </Button>

          <Button
            type="text"
            size="small"
            danger
            title="删除文章"
            @click="handleDelete(record)"
          >
            <template #icon>
              <Trash2 :size="14" />
            </template>
          </Button>
        </Space>
      </template>
    </template>
  </Table>
</template>

<style scoped>
:deep(.ant-table-cell) {
  padding: 12px 8px;
}

:deep(.ant-image) {
  display: block;
}

:deep(.ant-input-number) {
  width: 80px;
}

:deep(.ant-switch-small) {
  min-width: 44px;
}
</style>
