<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { Modal, Form, Input, Select, message } from 'ant-design-vue';
import type { ArticleCategory } from '#/types';
import { CATEGORY_WEIGHT_OPTIONS } from '#/types';

defineOptions({
  name: 'CategoryForm',
});

const props = defineProps<{
  visible: boolean;
  editingRecord?: ArticleCategory | null;
}>();

const emit = defineEmits<{
  'update:visible': [visible: boolean];
  cancel: [];
  confirm: [formData: any];
}>();

// 表单引用
const formRef = ref();

// 表单数据
const formData = ref({
  name: '',
  weight: '',
  seo_title: '',
  seo_keywords: '',
  seo_description: '',
});

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { max: 20, message: '分类名称不能超过20个字符', trigger: 'blur' },
  ],
  weight: [
    { required: true, message: '请选择分类权重', trigger: 'change' },
  ],
  seo_title: [
    { max: 100, message: 'SEO标题不能超过100个字符', trigger: 'blur' },
  ],
  seo_keywords: [
    { max: 100, message: 'SEO关键字不能超过100个字符', trigger: 'blur' },
  ],
  seo_description: [
    { max: 300, message: 'SEO描述不能超过300个字符', trigger: 'blur' },
  ],
};

// 计算属性：弹窗标题
const modalTitle = computed(() => {
  return props.editingRecord?.id ? '编辑标签分类' : '新增标签分类';
});

// 监听编辑记录变化
watch(
  () => props.editingRecord,
  (newRecord) => {
    if (newRecord) {
      formData.value = {
        name: newRecord.name || '',
        weight: newRecord.weight || '',
        seo_title: newRecord.seo_title || '',
        seo_keywords: newRecord.seo_keywords || '',
        seo_description: newRecord.seo_description || '',
      };
    } else {
      resetForm();
    }
  },
  { immediate: true }
);

// 监听弹窗显示状态
watch(
  () => props.visible,
  (visible) => {
    if (!visible) {
      resetForm();
    }
  }
);

// 重置表单
function resetForm() {
  formData.value = {
    name: '',
    weight: '',
    seo_title: '',
    seo_keywords: '',
    seo_description: '',
  };
  formRef.value?.resetFields();
}

// 确认提交
async function handleConfirm() {
  try {
    await formRef.value?.validate();

    const submitData: any = { ...formData.value };

    // 只有在编辑模式且有有效ID时才包含ID
    if (props.editingRecord?.id) {
      submitData.id = props.editingRecord.id;
    }

    emit('confirm', submitData);
  } catch (error) {
    console.error('表单验证失败:', error);
  }
}

// 取消
function handleCancel() {
  emit('update:visible', false);
  emit('cancel');
}

// 处理弹窗关闭
function handleModalCancel() {
  emit('update:visible', false);
  emit('cancel');
}
</script>

<template>
  <Modal
    :open="visible"
    :title="modalTitle"
    :width="600"
    :destroy-on-close="true"
    :mask-closable="false"
    @cancel="handleModalCancel"
    @ok="handleConfirm"
  >
    <Form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      layout="vertical"
      class="mt-4"
    >
      <Form.Item label="分类名称" name="name">
        <Input
          v-model:value="formData.name"
          placeholder="请输入分类名称（最多20个字符）"
          :maxlength="20"
          show-count
        />
      </Form.Item>

      <Form.Item label="分类权重" name="weight">
        <Select
          v-model:value="formData.weight"
          placeholder="请选择分类权重"
          :options="CATEGORY_WEIGHT_OPTIONS"
        />
      </Form.Item>
      
      <Form.Item label="SEO标题" name="seo_title">
        <Input 
          v-model:value="formData.seo_title" 
          placeholder="请输入SEO标题（最多100个字符）"
          :maxlength="100"
          show-count
        />
      </Form.Item>
      
      <Form.Item label="SEO关键字" name="seo_keywords">
        <Input 
          v-model:value="formData.seo_keywords" 
          placeholder="请输入SEO关键字，多个关键字用逗号分隔（最多100个字符）"
          :maxlength="100"
          show-count
        />
      </Form.Item>
      
      <Form.Item label="SEO描述" name="seo_description">
        <Input.TextArea 
          v-model:value="formData.seo_description" 
          placeholder="请输入SEO描述（最多300个字符）"
          :maxlength="300"
          :rows="4"
          show-count
        />
      </Form.Item>
    </Form>
  </Modal>
</template>
