<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { Modal, Form, Input, message } from 'ant-design-vue';
import type { ArticleAuthor } from '#/types';

defineOptions({
  name: 'AuthorForm',
});

const props = defineProps<{
  visible: boolean;
  editingRecord?: ArticleAuthor | null;
}>();

const emit = defineEmits<{
  'update:visible': [visible: boolean];
  cancel: [];
  confirm: [formData: any];
}>();

// 表单引用
const formRef = ref();

// 表单数据
const formData = ref({
  wechat_nickname: '',
  author_name: '',
});

// 表单验证规则
const formRules = {
  wechat_nickname: [
    { required: true, message: '请输入微信昵称', trigger: 'blur' },
    { max: 30, message: '微信昵称不能超过30个字符', trigger: 'blur' },
  ],
  author_name: [
    { required: true, message: '请输入作者名', trigger: 'blur' },
    { max: 30, message: '作者名不能超过30个字符', trigger: 'blur' },
  ],
};

// 计算属性：弹窗标题
const modalTitle = computed(() => {
  return props.editingRecord?.id ? '编辑作者' : '新增作者';
});

// 监听编辑记录变化
watch(
  () => props.editingRecord,
  (newRecord) => {
    if (newRecord) {
      formData.value = {
        wechat_nickname: newRecord.wechat_nickname || '',
        author_name: newRecord.author_name || '',
      };
    } else {
      resetForm();
    }
  },
  { immediate: true }
);

// 监听弹窗显示状态
watch(
  () => props.visible,
  (visible) => {
    if (!visible) {
      resetForm();
    }
  }
);

// 重置表单
function resetForm() {
  formData.value = {
    wechat_nickname: '',
    author_name: '',
  };
  formRef.value?.resetFields();
}

// 确认提交
async function handleConfirm() {
  try {
    await formRef.value?.validate();
    
    const submitData = { ...formData.value };
    
    // 只有在编辑模式且有有效ID时才包含ID
    if (props.editingRecord?.id) {
      submitData.id = props.editingRecord.id;
    }
    
    emit('confirm', submitData);
  } catch (error) {
    console.error('表单验证失败:', error);
  }
}

// 取消
function handleCancel() {
  emit('update:visible', false);
  emit('cancel');
}

// 处理弹窗关闭
function handleModalCancel() {
  emit('update:visible', false);
  emit('cancel');
}
</script>

<template>
  <Modal
    :open="visible"
    :title="modalTitle"
    :width="500"
    :destroy-on-close="true"
    :mask-closable="false"
    @cancel="handleModalCancel"
    @ok="handleConfirm"
  >
    <Form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      layout="vertical"
      class="mt-4"
    >
      <Form.Item label="微信昵称" name="wechat_nickname">
        <Input 
          v-model:value="formData.wechat_nickname" 
          placeholder="请输入微信昵称（最多30个字符）"
          :maxlength="30"
          show-count
        />
      </Form.Item>
      
      <Form.Item label="作者名" name="author_name">
        <Input 
          v-model:value="formData.author_name" 
          placeholder="请输入作者名（最多30个字符）"
          :maxlength="30"
          show-count
        />
      </Form.Item>
      
      <div class="text-sm text-gray-500">
        <p>• 微信昵称：用于显示在文章中的作者微信昵称</p>
        <p>• 作者名：用于后台管理的作者真实姓名</p>
      </div>
    </Form>
  </Modal>
</template>
