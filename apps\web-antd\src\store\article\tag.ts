// 标签管理 store
import { ref } from 'vue';
import { defineStore } from 'pinia';
import { requestClient } from '#/api/request';

// types
import type {
  ArticleTag,
  ArticleTagFormData,
  ArticleTagApiData,
  ArticleTagListParams,
  StanderResult,
} from '#/types';

// API响应类型
interface BatchDeleteResponse {
  deleted_count: number;
}

// API函数
function _createArticleTag(params: ArticleTagApiData) {
  return requestClient.post<StanderResult<ArticleTag>>(
    'article-tag/create',
    params,
  );
}

function _getArticleTagList(params: ArticleTagListParams = {}) {
  return requestClient.get<
    { total_records: number } & StanderResult<ArticleTag[]>
  >('article-tag/list', { params });
}

function _getArticleTagDetail(id: number) {
  return requestClient.get<StanderResult<ArticleTag>>(
    `article-tag/${id}`,
  );
}

function _updateArticleTag(id: number, params: Partial<ArticleTagApiData>) {
  return requestClient.put<StanderResult<ArticleTag>>(
    `article-tag/${id}`,
    params,
  );
}

function _deleteArticleTag(id: number) {
  return requestClient.delete<StanderResult<null>>(
    `article-tag/${id}`,
  );
}

function _batchDeleteArticleTag(ids: number[]) {
  return requestClient.delete<StanderResult<BatchDeleteResponse>>(
    'article-tag/batch-delete',
    { data: { ids } },
  );
}

// store
export const useArticleTagStore = defineStore('article-tag-store', () => {
  const loading = ref<boolean>(false);
  const tags = ref<ArticleTag[]>([]);
  const totalRecords = ref<number>(0);

  // 创建标签
  async function addArticleTag(params: ArticleTagFormData) {
    try {
      loading.value = true;
      // 直接使用表单数据，字段名已经统一
      const apiData: ArticleTagApiData = {
        name: params.name,
        category_id: params.category_id,
      };
      const res = await _createArticleTag(apiData);
      if (res.success) {
        // 创建成功后刷新列表
        await getArticleTagList();
        return res.data;
      }
      throw new Error(res.message || '创建失败');
    } finally {
      loading.value = false;
    }
  }

  // 获取标签列表
  async function getArticleTagList(params: ArticleTagListParams = {}) {
    try {
      loading.value = true;
      const res = await _getArticleTagList(params);
      if (res.success) {
        tags.value = res.data || [];
        totalRecords.value = res.total_records || 0;
        return res.data;
      }
      throw new Error(res.message || '获取列表失败');
    } finally {
      loading.value = false;
    }
  }

  // 获取标签详情
  async function getArticleTagDetail(id: number) {
    try {
      loading.value = true;
      const res = await _getArticleTagDetail(id);
      if (res.success) {
        return res.data;
      }
      throw new Error(res.message || '获取详情失败');
    } finally {
      loading.value = false;
    }
  }

  // 更新标签
  async function updateArticleTag(params: Partial<ArticleTag & ArticleTagFormData>) {
    try {
      loading.value = true;
      if (!params.id) {
        throw new Error('更新失败：缺少ID');
      }

      const id = params.id;

      // 直接使用表单数据，字段名已经统一
      const apiData: Partial<ArticleTagApiData> = {};
      if (params.name !== undefined) apiData.name = params.name;
      if (params.category_id !== undefined) apiData.category_id = params.category_id;

      const res = await _updateArticleTag(id, apiData);
      if (res.success) {
        // 更新成功后刷新列表
        await getArticleTagList();
        return res.data;
      }
      throw new Error(res.message || '更新失败');
    } finally {
      loading.value = false;
    }
  }

  // 删除标签
  async function deleteArticleTag(id: number) {
    try {
      loading.value = true;
      const res = await _deleteArticleTag(id);
      if (res.success) {
        // 删除成功后刷新列表
        await getArticleTagList();
        return true;
      }
      throw new Error(res.message || '删除失败');
    } finally {
      loading.value = false;
    }
  }

  // 批量删除标签
  async function batchDeleteArticleTag(ids: number[]) {
    try {
      loading.value = true;
      const res = await _batchDeleteArticleTag(ids);
      if (res.success) {
        // 删除成功后刷新列表
        await getArticleTagList();
        return res.data;
      }
      throw new Error(res.message || '批量删除失败');
    } finally {
      loading.value = false;
    }
  }

  return {
    loading,
    tags,
    totalRecords,
    addArticleTag,
    getArticleTagList,
    getArticleTagDetail,
    updateArticleTag,
    deleteArticleTag,
    batchDeleteArticleTag,
  };
});
