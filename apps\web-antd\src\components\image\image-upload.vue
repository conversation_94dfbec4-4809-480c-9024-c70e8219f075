<script setup lang="ts">
import { ref, computed } from 'vue';
import { Upload, Button, message } from 'ant-design-vue';
import { Upload as UploadIcon, Image, Trash2 } from 'lucide-vue-next';

// 导入组件
import ImageLibraryModal from './image-library-modal.vue';

// 导入store
import { useImageStore } from '#/store/image/image';

// 导入类型和常量
import type { ImageInfo } from '#/types/image';
import { SUPPORTED_IMAGE_TYPES, MAX_IMAGE_SIZE } from '#/types/image';

defineOptions({
  name: 'ImageUpload',
});

const props = defineProps<{
  modelValue?: string;
  placeholder?: string;
  category?: string;
  disabled?: boolean;
}>();

const emit = defineEmits<{
  'update:modelValue': [value: string];
  change: [value: string, imageInfo?: ImageInfo];
}>();

const imageStore = useImageStore();

// 组件状态
const uploading = ref(false);
const showLibraryModal = ref(false);

// 计算属性
const imageUrl = computed(() => props.modelValue);
const hasImage = computed(() => !!props.modelValue);

// 处理本地文件上传
async function handleLocalUpload(file: File) {
  try {
    // 验证文件类型
    if (!SUPPORTED_IMAGE_TYPES.includes(file.type)) {
      message.error('不支持的图片格式，请上传 JPG、PNG、GIF 或 WebP 格式的图片');
      return false;
    }

    // 验证文件大小
    if (file.size > MAX_IMAGE_SIZE) {
      message.error('图片大小不能超过 10MB');
      return false;
    }

    uploading.value = true;

    const imageInfo = await imageStore.uploadImage({
      file,
      original_name: file.name,
      category: props.category || 'article',
      is_public: true,
    });

    // 更新值
    emit('update:modelValue', imageInfo.access_url);
    emit('change', imageInfo.access_url, imageInfo);

    message.success('图片上传成功');
    return false; // 阻止默认上传行为
  } catch (error) {
    message.error('图片上传失败，请重试');
    console.error('上传失败:', error);
    return false;
  } finally {
    uploading.value = false;
  }
}

// 处理从图片库选择
function handleSelectFromLibrary() {
  showLibraryModal.value = true;
}

// 处理图片库选择结果
function handleLibrarySelect(imageInfo: ImageInfo) {
  emit('update:modelValue', imageInfo.access_url);
  emit('change', imageInfo.access_url, imageInfo);
  showLibraryModal.value = false;
}

// 处理移除图片
function handleRemove() {
  emit('update:modelValue', '');
  emit('change', '');
}
</script>

<template>
  <div class="image-upload">
    <!-- 已有图片时的显示 -->
    <div v-if="hasImage" class="image-preview-container">
      <div class="image-preview">
        <img :src="imageUrl" alt="预览图片" class="preview-image" />
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <Upload
          :before-upload="handleLocalUpload"
          :show-upload-list="false"
          accept="image/*"
          :disabled="disabled || uploading"
        >
          <Button size="small" :loading="uploading">
            <UploadIcon :size="14" />
            重新上传
          </Button>
        </Upload>

        <Button size="small" @click="handleSelectFromLibrary">
          <Image :size="14" />
          图片库
        </Button>

        <Button size="small" danger @click="handleRemove">
          <Trash2 :size="14" />
          移除
        </Button>
      </div>
    </div>

    <!-- 无图片时的上传区域 -->
    <div v-else class="upload-area">
      <div class="upload-buttons">
        <Upload
          :before-upload="handleLocalUpload"
          :show-upload-list="false"
          accept="image/*"
          :disabled="disabled || uploading"
        >
          <Button type="dashed" :loading="uploading" class="upload-btn">
            <UploadIcon :size="16" />
            {{ placeholder || '本地上传' }}
          </Button>
        </Upload>

        <Button type="dashed" @click="handleSelectFromLibrary" class="upload-btn">
          <Image :size="16" />
          图片库
        </Button>
      </div>
    </div>

    <!-- 图片库弹窗 -->
    <ImageLibraryModal
      v-model:visible="showLibraryModal"
      @select="handleLibrarySelect"
    />
  </div>
</template>

<style scoped>
.image-upload {
  display: inline-block;
}

.image-preview-container {
  width: 200px;
}

.image-preview {
  width: 200px;
  height: 120px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 8px;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.action-buttons {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.upload-area {
  width: 200px;
}

.upload-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.upload-btn {
  width: 100%;
  height: 60px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

:deep(.ant-upload) {
  display: block;
  width: 100%;
}

:deep(.ant-btn) {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}
</style>
