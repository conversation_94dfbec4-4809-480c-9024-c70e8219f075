{"name": "@vben/stylelint-config", "version": "5.3.0-beta.2", "private": true, "homepage": "https://github.com/vbenjs/vue-vben-admin", "bugs": "https://github.com/vbenjs/vue-vben-admin/issues", "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "internal/lint-configs/stylelint-config"}, "license": "MIT", "type": "module", "files": ["dist"], "main": "./index.mjs", "module": "./index.mjs", "exports": {".": {"import": "./index.mjs", "default": "./index.mjs"}}, "dependencies": {"@stylistic/stylelint-plugin": "^3.0.1", "stylelint-config-recess-order": "^5.1.0", "stylelint-scss": "^6.6.0"}, "devDependencies": {"postcss": "^8.4.47", "postcss-html": "^1.7.0", "postcss-scss": "^4.0.9", "prettier": "^3.3.3", "stylelint": "^16.9.0", "stylelint-config-recommended": "^14.0.1", "stylelint-config-recommended-scss": "^14.1.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^36.0.1", "stylelint-order": "^6.0.4", "stylelint-prettier": "^5.0.2"}}