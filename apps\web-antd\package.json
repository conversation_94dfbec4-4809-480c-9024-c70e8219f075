{"name": "@vben/web-antd", "version": "5.3.0-beta.2", "homepage": "https://vben.pro", "bugs": "https://github.com/vbenjs/vue-vben-admin/issues", "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "apps/web-antd"}, "license": "MIT", "author": {"name": "vben", "email": "<EMAIL>", "url": "https://github.com/anncwb"}, "type": "module", "scripts": {"build": "npx vite build --mode production", "build:analyze": "npx vite build --mode analyze", "dev": "npx vite --mode development", "dev:test": "npx vite --mode test", "preview": "vite preview", "typecheck": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>"}, "imports": {"#/*": "./src/*"}, "dependencies": {"@types/crypto-js": "^4.2.2", "@vben/access": "workspace:*", "@vben/common-ui": "workspace:*", "@vben/constants": "workspace:*", "@vben/hooks": "workspace:*", "@vben/icons": "workspace:*", "@vben/layouts": "workspace:*", "@vben/locales": "workspace:*", "@vben/plugins": "workspace:*", "@vben/preferences": "workspace:*", "@vben/request": "workspace:*", "@vben/stores": "workspace:*", "@vben/styles": "workspace:*", "@vben/types": "workspace:*", "@vben/utils": "workspace:*", "@vueuse/core": "^11.1.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "ant-design-vue": "^4.2.4", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "file-saver": "^2.0.5", "html2pdf.js": "^0.10.2", "lucide-vue-next": "^0.441.0", "pinia": "2.2.2", "vue": "^3.5.6", "vue-router": "^4.4.5", "vue-virtual-scroller": "2.0.0-beta.8"}}