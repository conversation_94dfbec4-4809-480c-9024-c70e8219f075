{"name": "@vben/node-utils", "version": "5.3.0-beta.2", "private": true, "homepage": "https://github.com/vbenjs/vue-vben-admin", "bugs": "https://github.com/vbenjs/vue-vben-admin/issues", "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "internal/node-utils"}, "license": "MIT", "type": "module", "scripts": {"stub": "pnpm unbuild --stub"}, "files": ["dist"], "main": "./dist/index.mjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"types": "./src/index.ts", "import": "./dist/index.mjs", "default": "./dist/index.mjs"}}, "dependencies": {"@changesets/git": "^3.0.1", "@manypkg/get-packages": "^2.2.2", "chalk": "^5.3.0", "consola": "^3.2.3", "dayjs": "^1.11.13", "execa": "^9.4.0", "find-up": "^7.0.0", "nanoid": "^5.0.7", "ora": "^8.1.0", "pkg-types": "^1.2.0", "prettier": "^3.3.3", "rimraf": "^6.0.1"}, "devDependencies": {"@types/chalk": "^2.2.0"}}