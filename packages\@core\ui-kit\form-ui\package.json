{"name": "@vben-core/form-ui", "version": "5.2.1", "homepage": "https://github.com/vbenjs/vue-vben-admin", "bugs": "https://github.com/vbenjs/vue-vben-admin/issues", "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "packages/@vben-core/uikit/form-ui"}, "license": "MIT", "type": "module", "scripts": {"build": "pnpm unbuild", "prepublishOnly": "npm run build"}, "files": ["dist"], "sideEffects": ["**/*.css"], "main": "./dist/index.mjs", "module": "./dist/index.mjs", "exports": {".": {"types": "./src/index.ts", "development": "./src/index.ts", "default": "./dist/index.mjs"}}, "publishConfig": {"exports": {".": {"default": "./dist/index.mjs"}}}, "dependencies": {"@vben-core/composables": "workspace:*", "@vben-core/shadcn-ui": "workspace:*", "@vben-core/shared": "workspace:*", "@vee-validate/zod": "^4.13.2", "@vueuse/core": "^11.1.0", "vee-validate": "^4.13.2", "vue": "^3.5.6", "zod": "^3.23.8", "zod-defaults": "^0.1.3"}}