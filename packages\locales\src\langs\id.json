{"page": {"core": {"login": "<PERSON><PERSON>", "register": "daftar", "codeLogin": "<PERSON>gin kode verifikasi", "qrcodeLogin": "Masuk kode QR", "forgetPassword": "lupa kata sandinya"}, "dashboard": {"title": "<PERSON><PERSON><PERSON>", "analytics": "Halaman analisis", "workspace": "meja kerja"}, "vben": {"title": "proyek", "about": "tentang", "document": "dokumen", "antdv": "<PERSON><PERSON><PERSON>", "naive-ui": "Versi UI naif", "element-plus": "Versi Elemen Plus"}}, "common": {"back": "kembali", "backToHome": "<PERSON><PERSON><PERSON> ke halaman beranda", "login": "<PERSON><PERSON><PERSON>", "logout": "<PERSON><PERSON><PERSON>", "prompt": "petunjuk", "cancel": "Membatalkan", "confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "noData": "Belum ada datanya", "refresh": "menyegarkan", "loadingMenu": "memuat menu"}, "fallback": {"pageNotFound": "ups! ", "pageNotFoundDesc": "<PERSON><PERSON>, kami tidak dapat menemukan halaman yang <PERSON>a cari.", "forbidden": "ups! ", "forbiddenDesc": "<PERSON><PERSON>, <PERSON>a tidak memiliki izin untuk mengakses halaman ini.", "internalError": "ups! ", "internalErrorDesc": "<PERSON><PERSON>, server men<PERSON><PERSON> k<PERSON><PERSON>han.", "offline": "halaman luring", "offlineError": "ups! ", "offlineErrorDesc": "Maaf, tidak dapat terhubung ke internet, periksa koneksi jaringan Anda dan coba lagi.", "comingSoon": "segera hadir", "http": {"requestTimeout": "<PERSON><PERSON><PERSON> permin<PERSON>an telah habis, harap coba lagi nanti.", "networkError": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaringan, silakan periksa koneksi jaringan Anda dan coba lagi.", "badRequest": "<PERSON><PERSON><PERSON> per<PERSON>. ", "unauthorized": "Otentikasi login telah ked<PERSON>, silakan login lagi untuk melanjutkan.", "forbidden": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> tidak memiliki izin untuk mengakses sumber daya ini.", "notFound": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> daya yang diminta tidak ada.", "internalServerError": "Kesalahan server internal, coba lagi nanti."}}, "formRules": {"required": "<PERSON><PERSON><PERSON> ma<PERSON> {0}"}, "widgets": {"document": "dokumen", "qa": "Pertanyaan & Bantuan", "setting": "<PERSON><PERSON><PERSON><PERSON>", "logoutTip": "<PERSON><PERSON><PERSON>?", "viewAll": "<PERSON><PERSON> semua pesan", "notifications": "<PERSON><PERSON><PERSON>", "markAllAsRead": "Tandai semua sebagai telah dibaca", "clearNotifications": "<PERSON><PERSON><PERSON>", "checkUpdatesTitle": "Versi baru tersedia", "checkUpdatesDescription": "Klik segarkan untuk mendapatkan versi terbaru", "search": {"title": "<PERSON>cari", "searchNavigate": "<PERSON>u navigasi pencarian", "select": "memilih", "navigate": "navigasi", "close": "penutup", "noResults": "Tidak ada hasil pencarian yang di<PERSON>n", "noRecent": "Tidak ada riwayat pencarian", "recent": "Riwayat pencarian"}, "lockScreen": {"title": "layar kunci", "screenButton": "<PERSON><PERSON><PERSON>", "password": "kata sandi", "placeholder": "<PERSON><PERSON><PERSON> masukkan kata sandi layar kunci", "unlock": "Klik untuk membuka kunci", "errorPasswordTip": "<PERSON>a sandi salah, silakan masukkan kembali", "backToLogin": "Kembali untuk masuk", "entry": "Masuk ke sistem"}}, "authentication": {"welcomeBack": "Selamat Datang kembali", "pageTitle": "Sistem manajemen mid-end dan back-end be<PERSON><PERSON> besar yang out-of-the-box", "pageDesc": "Templat front-end perpustakaan lintas komponen yang dire<PERSON>, be<PERSON><PERSON><PERSON> tinggi", "loginSuccess": "<PERSON><PERSON><PERSON>", "loginSuccessDesc": "Selamat Datang kembali", "loginSubtitle": "<PERSON><PERSON><PERSON> masukkan informasi akun Anda untuk mulai mengelola proyek Anda", "selectAccount": "<PERSON><PERSON><PERSON> akun dengan cepat", "username": "akun", "password": "kata sandi", "usernameTip": "<PERSON><PERSON><PERSON> masukkan nama pengguna", "passwordTip": "<PERSON><PERSON><PERSON> masukkan kata sandi", "passwordErrorTip": "Kata sandi salah", "rememberMe": "ingat aku", "createAnAccount": "<PERSON><PERSON><PERSON> akun", "createAccount": "<PERSON><PERSON><PERSON> akun", "alreadyHaveAccount": "Sudah punya akun?", "accountTip": "Belum punya akun?", "signUp": "daftar", "signUpSubtitle": "Jadikan pengelolaan aplikasi Anda mudah dan menyenangkan", "confirmPassword": "<PERSON>n<PERSON><PERSON><PERSON>", "confirmPasswordTip": "Kata sandi yang dimasukkan dua kali tidak konsisten", "agree": "<PERSON><PERSON>", "privacyPolicy": "kebijakan privasi", "terms": "Ketentuan", "agreeTip": "<PERSON><PERSON><PERSON> dan <PERSON>", "goToLogin": "<PERSON><PERSON> untuk masuk", "passwordStrength": "Gunakan 8 karakter at<PERSON> lebih, campur huruf, angka, dan simbol", "forgetPassword": "lupa kata sandinya?", "forgetPasswordSubtitle": "Ma<PERSON>kka<PERSON> email Anda dan kami akan mengirimkan Anda tautan untuk mengatur ulang kata sandi Anda", "emailTip": "<PERSON><PERSON><PERSON> ma<PERSON> email <PERSON><PERSON>", "emailValidErrorTip": "Format email yang <PERSON>a masukkan salah", "sendResetLink": "<PERSON><PERSON> tautan setel ulang", "email": "Surat", "qrcodeSubtitle": "<PERSON><PERSON><PERSON> gunakan ponsel Anda untuk memindai kode QR untuk masuk", "qrcodePrompt": "<PERSON><PERSON><PERSON> me<PERSON>ai kode QR, klik 'Konfirmasi' untuk menyelesaikan login", "qrcodeLogin": "Pindai kode untuk masuk", "codeSubtitle": "<PERSON><PERSON><PERSON> masukkan nomor ponsel Anda untuk mulai mengelola proyek Anda", "code": "<PERSON><PERSON> veri<PERSON>", "codeTip": "<PERSON><PERSON><PERSON> masukkan kode verifikasi", "mobile": "nomor telepon", "mobileTip": "<PERSON><PERSON><PERSON> masukkan nomor ponsel", "mobileErrortip": "Kesalahan format nomor ponsel", "mobileLogin": "Login nomor ponsel", "sendCode": "Dapatkan kode verifikasi", "sendText": "<PERSON><PERSON> {0} detik", "thirdPartyLogin": "Metode masuk la<PERSON>", "loginAgainTitle": "<PERSON><PERSON>k lagi", "loginAgainSubTitle": "Status login <PERSON>a telah ked<PERSON>, silakan login kembali untuk melanjutkan.", "layout": {"center": "tengah", "alignLeft": "Di sebelah kiri", "alignRight": "<PERSON><PERSON><PERSON> kanan"}}, "preferences": {"title": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "Preferensi k<PERSON> & prat<PERSON><PERSON><PERSON> langsung", "resetTitle": "<PERSON><PERSON> prefer<PERSON>i", "resetTip": "Data telah be<PERSON>, klik untuk mereset", "resetSuccess": "Reset preferensi berhasil", "appearance": "Penampilan", "layout": "tata letak", "content": "isi", "other": "la<PERSON><PERSON>", "wide": "<PERSON><PERSON><PERSON>", "compact": "lebar tetap", "followSystem": "<PERSON><PERSON><PERSON>", "vertical": "vertikal", "verticalTip": "Mode menu vertikal samping", "horizontal": "tingkat", "horizontalTip": "Mode menu horizontal, semua menu ditampilkan di bagian atas", "twoColumn": "<PERSON>u kolom ganda", "twoColumnTip": "Mode menu kolom ganda vertikal", "mixedMenu": "menu campuran", "mixedMenuTip": "Koeksistensi menu vertikal dan horizontal", "fullContent": "<PERSON><PERSON>n layar penuh", "fullContentTip": "Tidak ada menu yang ditampilkan, hanya isi konten yang ditampilkan", "normal": "konvensional", "plain": "se<PERSON><PERSON>", "rounded": "lembut", "copyPreferences": "<PERSON><PERSON>i", "copyPreferencesSuccess": "<PERSON><PERSON><PERSON><PERSON> di<PERSON><PERSON>, harap timpa di `src/preferences.ts` di bawah aplikasi", "clearAndLogout": "Hapus cache & keluar", "mode": "model", "general": "Universal", "language": "bahasa", "dynamicTitle": "judul dinamis", "watermark": "tanda air", "checkUpdates": "<PERSON>ik<PERSON> pembaruan secara teratur", "position": {"title": "Lokasi preferensi", "header": "bilah atas", "auto": "otomatis", "fixed": "tetap"}, "sidebar": {"title": "bilah sisi", "width": "lebar", "visible": "<PERSON><PERSON><PERSON><PERSON> bilah sisi", "collapsed": "Ciutkan menu", "collapsedShowTitle": "Ciutkan nama menu"}, "tabbar": {"title": "bilah tab", "enable": "Aktifkan bilah tab", "icon": "<PERSON><PERSON><PERSON><PERSON> ikon bilah tab", "showMore": "<PERSON><PERSON><PERSON><PERSON> tombol la<PERSON>ya", "showRefresh": "<PERSON><PERSON><PERSON><PERSON> tombol segarkan", "showMaximize": "<PERSON><PERSON><PERSON><PERSON> tombol maksima<PERSON>an", "persist": "tab ketekunan", "dragable": "<PERSON><PERSON> seret", "styleType": {"title": "Gaya tab", "chrome": "Google", "card": "kartu", "plain": "se<PERSON><PERSON>", "brisk": "cepat"}, "contextMenu": {"reload": "memuat ulang", "close": "penutup", "pin": "tetap", "unpin": "Membuka peniti", "closeLeft": "Tutup tab kiri", "closeRight": "Tutup tab di sebelah kanan", "closeOther": "Tutup tab lainnya", "closeAll": "Tutup semua tab", "openInNewWindow": "<PERSON>uka di jendela baru", "maximize": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "restoreMaximize": "<PERSON><PERSON><PERSON><PERSON>"}}, "navigationMenu": {"title": "<PERSON><PERSON> navigasi", "style": "Gaya menu navigasi", "accordion": "Mode akordeon menu navigasi samping", "split": "Pem<PERSON>han menu navigasi", "splitTip": "Saat dihidupkan, sidebar menampilkan submenu menu yang sesuai dengan bar bagian atas."}, "breadcrumb": {"title": "Tepung roti", "enable": "Aktifkan navigasi remah roti", "icon": "<PERSON><PERSON><PERSON><PERSON> ikon remah roti", "home": "<PERSON><PERSON><PERSON><PERSON> tombol beranda", "style": "<PERSON><PERSON> remah roti", "hideOnlyOne": "Sembunyikan saat hanya ada satu", "background": "latar belakang"}, "animation": {"title": "animasi", "loading": "<PERSON><PERSON><PERSON>", "transition": "<PERSON><PERSON><PERSON> per<PERSON> halaman", "progress": "<PERSON><PERSON><PERSON> k<PERSON> per<PERSON> halaman"}, "theme": {"title": "tema", "radius": "sudut membulat", "light": "warna terang", "dark": "<PERSON><PERSON><PERSON>", "darkSidebar": "bilah sisi gelap", "darkHeader": "bilah atas yang gelap", "weakMode": "<PERSON> Kelem<PERSON>", "grayMode": "modus abu-abu", "builtin": {"title": "<PERSON><PERSON> bawaan", "default": "bawaan", "violet": "ungu", "pink": "bubuk sakura", "rose": "mawar merah", "skyBlue": "biru langit", "deepBlue": "biru tua", "green": "hijau muda", "deepGreen": "hijau tua", "orange": "kuning oranye", "yellow": "kuning lemon", "zinc": "seng abu-abu", "neutral": "warna netral", "slate": "abu-abu batu tulis", "gray": "abu-abu sedang", "custom": "Sesuai<PERSON>"}}, "header": {"title": "bilah atas", "modeStatic": "tetap", "modeFixed": "tetap", "modeAuto": "Sembunyikan dan tampilkan secara otomatis", "modeAutoScroll": "<PERSON><PERSON><PERSON> untuk menyembunyikan dan menampilkan", "visible": "<PERSON><PERSON><PERSON><PERSON> bilah atas"}, "footer": {"title": "bilah bawah", "visible": "<PERSON><PERSON><PERSON><PERSON> bilah bawah", "fixed": "diperbaiki di bagian bawah"}, "copyright": {"title": "hak cipta", "enable": "Aktifkan hak cipta", "companyName": "<PERSON><PERSON>", "companySiteLink": "<PERSON><PERSON><PERSON>", "date": "tanggal", "icp": "Nomor registrasi ICP", "icpLink": "Tautan situs web ICP"}, "shortcutKeys": {"title": "tombol pintas", "global": "situasi k<PERSON>han", "search": "pencarian global", "logout": "<PERSON><PERSON><PERSON>", "preferences": "<PERSON><PERSON><PERSON><PERSON>"}, "widget": {"title": "widget", "globalSearch": "Aktifkan pencarian global", "fullscreen": "Aktif<PERSON> layar penuh", "themeToggle": "<PERSON><PERSON><PERSON><PERSON> per<PERSON>han tema", "languageToggle": "<PERSON><PERSON><PERSON><PERSON> bahasa", "notification": "Aktifkan notifikasi", "sidebarToggle": "Aktifkan sakelar sidebar", "lockScreen": "Aktifkan layar kunci"}}, "captcha": {"alt": "Mendukung nilai atribut tag img src", "title": "<PERSON><PERSON><PERSON> se<PERSON>kan verifi<PERSON>i keamanan", "refreshAriaLabel": "Segarkan kode verifikasi", "confirmAriaLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>", "confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pointAriaLabel": "titik klik", "clickInOrder": "Silakan klik"}}