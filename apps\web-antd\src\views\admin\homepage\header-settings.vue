<script setup lang="ts">
import { nextTick, onMounted, reactive, ref } from 'vue';
import { Card, message } from 'ant-design-vue';
import { Database } from 'lucide-vue-next';

// 导入全局组件
import HeaderSettingsTabs from '#/components/header-settings-tabs.vue';
import HeaderLinksTable from '#/components/header-links-table.vue';
import HeaderLinkForm from '#/components/header-link-form.vue';
import HeaderLinkGroupForm from '#/components/header-link-group-form.vue';

// stores
import { useSiteConfigStore } from '#/store/siteconfig';

// types
import type { SiteConfig } from '#/types';

defineOptions({
  name: 'HeaderSettings',
});

// 使用store
const siteConfigStore = useSiteConfigStore();

// 当前激活的tab
const activeTab = ref('header-links');

// 新增/编辑弹窗相关
const addModalVisible = ref(false);
const editingRecord = ref<null | SiteConfig>(null); // 当前编辑的记录

// 新增链接弹窗相关
const addLinkModalVisible = ref(false);
const editingLinkIndex = ref(-1); // -1表示新增，其他值表示编辑的索引

// 当前编辑的链接组数据
const editingLinkData = ref<{ name: string; url: string } | null>(null);

// 临时存储当前表单数据
const currentFormData = ref<any>(null);

// 处理获取当前表单数据
function handleGetCurrentFormData(formData: any) {
  currentFormData.value = formData;
}

// tab配置
const tabs = [
  {
    description: '管理网站头部的导航链接',
    key: 'header-links',
    label: '头部链接',
  },
  {
    description: '配置网站的基本信息和设置',
    key: 'site-config',
    label: '网站配置',
  },
  {
    description: '配置网站的功能开关和参数',
    key: 'feature-config',
    label: '功能配置',
  },
];

// 处理添加按钮点击
function handleAdd() {
  editingRecord.value = null;
  addModalVisible.value = true;
}

// 处理编辑操作
function handleEdit(record: SiteConfig) {
  editingRecord.value = JSON.parse(JSON.stringify(record));
  addModalVisible.value = true;
}

// 处理添加链接按钮点击
function handleAddLink() {
  editingLinkIndex.value = -1;
  editingLinkData.value = null;
  addLinkModalVisible.value = true;
}

// 处理编辑链接按钮点击
function handleEditLink(index: number) {
  editingLinkIndex.value = index;
  // 获取要编辑的链接组数据
  if (editingRecord.value?.link_groups && editingRecord.value.link_groups[index]) {
    editingLinkData.value = { ...editingRecord.value.link_groups[index] };
  } else if (currentFormData.value?.links && currentFormData.value.links[index]) {
    editingLinkData.value = { ...currentFormData.value.links[index] };
  }
  addLinkModalVisible.value = true;
}

// 刷新数据
async function handleRefresh() {
  try {
    await siteConfigStore.getSiteConfigList();
    message.success('数据刷新成功');
  } catch {
    message.error('刷新失败');
  }
}

// Tab切换
function handleTabChange(tabKey: string) {
  activeTab.value = tabKey;
}

// 删除链接
async function handleDelete(id: string) {
  try {
    await siteConfigStore.deleteSiteConfig(id);
    message.success('删除成功');
    await handleRefresh();
  } catch {
    message.error('删除失败');
  }
}

// 切换状态
async function handleToggleStatus(record: SiteConfig) {
  try {
    await siteConfigStore.updateSiteConfig({
      ...record,
      is_enabled: !record.is_enabled,
    });
    message.success('状态更新成功');
    await handleRefresh();
  } catch {
    message.error('状态更新失败');
  }
}

// 添加/编辑确认
async function handleAddConfirm(formData: any) {
  try {
    if (editingRecord.value && editingRecord.value.id) {
      // 编辑模式 - 包含ID
      await siteConfigStore.updateSiteConfig({
        ...editingRecord.value,
        ...formData,
        id: editingRecord.value.id, // 确保包含ID
        // 确保link_groups数据正确传递
        link_groups: formData.links?.map((link: any) => ({
          name: link.name,
          url: link.url
        })) || []
      });
      message.success('更新成功');
    } else {
      // 新增模式 - 不包含ID，让后端生成
      const newData = {
        ...formData,
        // 确保link_groups数据正确传递
        link_groups: formData.links?.map((link: any) => ({
          name: link.name,
          url: link.url
        })) || []
      };
      
      // 移除可能存在的空ID字段
      if (newData.id === '' || newData.id === undefined) {
        delete newData.id;
      }
      
      await siteConfigStore.addSiteConfig(newData);
      message.success('添加成功');
    }
    
    // 关闭弹窗并刷新数据
    addModalVisible.value = false;
    editingRecord.value = null;
    await handleRefresh();
  } catch (error) {
    console.error('保存失败:', error);
    message.error(editingRecord.value?.id ? '更新失败' : '添加失败');
  }
}

// 取消添加/编辑
function handleAddCancel() {
  addModalVisible.value = false;
  editingRecord.value = null;
}

// 取消添加链接
function handleAddLinkCancel() {
  addLinkModalVisible.value = false;
  editingLinkIndex.value = -1;
  editingLinkData.value = null;
}

// 删除链接组
function handleDeleteLink(index: number) {
  // 这个方法现在由HeaderLinkForm组件内部处理
}

// 添加/编辑链接组确认
function handleAddLinkConfirm(formData: any, index: number) {
  // 如果有当前表单数据，使用它；否则创建新的
  if (!editingRecord.value && currentFormData.value) {
    editingRecord.value = {
      id: '',
      title: currentFormData.value.title || '',
      link_url: currentFormData.value.link_url || '',
      prompt_text: currentFormData.value.prompt_text || '',
      is_enabled: currentFormData.value.is_enabled ?? true,
      partner_code: currentFormData.value.partner_code || '',
      link_groups: currentFormData.value.links || []
    } as SiteConfig;
  } else if (!editingRecord.value) {
    editingRecord.value = {
      id: '',
      title: '',
      link_url: '',
      prompt_text: '',
      is_enabled: true,
      partner_code: '',
      link_groups: []
    } as SiteConfig;
  }
  
  // 确保link_groups数组存在
  if (!editingRecord.value.link_groups) {
    editingRecord.value.link_groups = [];
  }
  
  if (index === -1) {
    // 新增
    editingRecord.value.link_groups.push({
      name: formData.name,
      url: formData.url,
    });
  } else {
    // 编辑
    editingRecord.value.link_groups[index] = {
      name: formData.name,
      url: formData.url,
    };
  }
  
  // 关闭弹窗
  addLinkModalVisible.value = false;
  editingLinkIndex.value = -1;
  
  // 强制更新视图
  editingRecord.value = { ...editingRecord.value };
}

// 页面初始化
onMounted(async () => {
  await handleRefresh();
});
</script>

<template>
  <div class="p-6">
    <!-- Tab切换 -->
    <Card>
      <HeaderSettingsTabs 
        :active-tab="activeTab" 
        :loading="siteConfigStore.loading"
        @tab-change="handleTabChange"
        @refresh="handleRefresh"
        @add="handleAdd"
      />

      <!-- 头部链接内容 -->
      <div v-if="activeTab === 'header-links'" class="space-y-6">
        <HeaderLinksTable 
          :site-configs="siteConfigStore.siteConfigs"
          :loading="siteConfigStore.loading"
          @edit="handleEdit"
          @delete="handleDelete"
          @toggle-status="handleToggleStatus"
        />
      </div>

      <!-- 其他tab内容 -->
      <div v-else class="py-12 text-center">
        <div class="mx-auto max-w-md">
          <div class="mb-4 text-gray-400">
            <Database :size="48" class="mx-auto" />
          </div>
          <h3 class="mb-2 text-lg font-medium text-gray-900">
            {{ tabs.find((tab) => tab.key === activeTab)?.label }}
          </h3>
          <p class="text-gray-500">此功能正在开发中，敬请期待...</p>
        </div>
      </div>
    </Card>

    <!-- 新增/编辑链接弹窗 -->
    <HeaderLinkForm
      v-model:visible="addModalVisible"
      :editing-record="editingRecord"
      @cancel="handleAddCancel"
      @confirm="handleAddConfirm"
      @addLink="handleAddLink"
      @editLink="handleEditLink"
      @deleteLink="handleDeleteLink"
      @getCurrentFormData="handleGetCurrentFormData"
    />

    <!-- 新增链接弹窗 -->
    <HeaderLinkGroupForm
      v-model:visible="addLinkModalVisible"
      :editing-index="editingLinkIndex"
      :editing-data="editingLinkData"
      @cancel="handleAddLinkCancel"
      @confirm="handleAddLinkConfirm"
    />
  </div>
</template>

<style scoped>
/* 表格样式优化 */
:deep(.ant-table-thead > tr > th) {
  font-size: 13px;
  font-weight: 600;
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 12px 16px;
  border-bottom: 1px solid #e2e8f0;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background-color: #f1f5f9;
}

:deep(.ant-pagination) {
  margin-top: 24px;
  text-align: center;
}

:deep(.ant-table) {
  overflow: hidden;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
}
</style>





























