// base
import { ref } from 'vue';

import CryptoJS from 'crypto-js';
import { defineStore } from 'pinia';

import { requestClient } from '#/api/request';

/*
图片示例
https://images.branddb.wipo.int/brands/phtm/PH502020000510743/3E048433-th.jpg
https://images.branddb.wipo.int/brands/mytm/MY502023000023756/6D63084D-hi.png

collection/st13/logo[1]-th.jpg
collection/st13/logo[1]-hi.png

*/

// types
import type {
  BrandQuery,
  BrandQueryResult,
  ImageBase64DataRequest,
  StanderResult,
  WIPOObject,
  WIPOResult,
} from '#/types';

// API
function _brandquery(params: BrandQuery) {
  return requestClient.post<StanderResult<BrandQueryResult>>(
    `brand/query`,
    params,
  );
}

function _imageBase64Data(params: ImageBase64DataRequest) {
  return requestClient.post<StanderResult<{ base64: string }>>(
    `brand/image-to-base64`,
    params,
  );
}

function _brandquery_decrypt(params: BrandQueryResult) {
  const key = CryptoJS.enc.Utf8.parse(params.hash);
  const decrypted = CryptoJS.AES.decrypt(params.response, key, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7,
  });
  const result = decrypted.toString(CryptoJS.enc.Utf8);
  return JSON.parse(result);
}
// store
export const useBrandStore = defineStore('brand-store', () => {
  // product_id: <name, path>
  const brands = ref<Array<WIPOObject>>([]);

  async function brandquery(params: BrandQuery) {
    const res = await _brandquery(params);
    if (res.success) {
      const data: WIPOResult = _brandquery_decrypt(res.data);
      const hashsearch = res.data.hash;
      brands.value = data.response.docs;
      for (const item of brands.value) {
        item.hashsearch = hashsearch;
      }
    }
  }

  async function imageBase64Data(params: ImageBase64DataRequest) {
    const res = await _imageBase64Data(params);
    return res.data.base64;
  }

  function $reset() {
    brands.value = [];
  }

  return {
    $reset,
    brandquery,
    brands,
    imageBase64Data,
  };
});
