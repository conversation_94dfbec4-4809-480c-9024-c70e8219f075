<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { 
  Modal, 
  Form, 
  Input, 
  Select, 
  Button, 
  Space,
  message,
  Image 
} from 'ant-design-vue';

// 导入store
import { useImageStore } from '#/store/image/image';

// 导入类型
import type { ImageInfo, ImageTag, ImageUpdateParams } from '#/types/image';

defineOptions({
  name: 'ImageEditModal',
});

const props = defineProps<{
  visible: boolean;
  imageInfo?: ImageInfo | null;
}>();

const emit = defineEmits<{
  'update:visible': [visible: boolean];
  'edit-success': [image: ImageInfo];
}>();

const imageStore = useImageStore();

// 组件状态
const loading = ref(false);

// 表单数据
const formData = ref({
  original_name: '',
  description: '',
  tag_ids: [] as number[],
});

// 表单引用
const formRef = ref();

// 表单验证规则
const formRules = {
  original_name: [
    { required: true, message: '请输入图片名称', trigger: 'blur' },
    { max: 100, message: '图片名称不能超过100个字符', trigger: 'blur' },
  ],
  description: [
    { max: 500, message: '图片描述不能超过500个字符', trigger: 'blur' },
  ],
};

// 计算属性
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
});

const tagOptions = computed(() => 
  imageStore.tags.map(tag => ({
    label: tag.name,
    value: tag.id,
  }))
);

// 监听图片信息变化，更新表单数据
watch(
  () => props.imageInfo,
  (imageInfo) => {
    if (imageInfo) {
      formData.value = {
        original_name: imageInfo.original_name || '',
        description: imageInfo.description || '',
        tag_ids: imageInfo.tags?.map(tag => tag.id) || [],
      };
    }
  },
  { immediate: true }
);

// 监听弹窗显示状态，加载标签数据
watch(
  () => props.visible,
  async (visible) => {
    if (visible) {
      // 确保标签数据已加载
      if (imageStore.tags.length === 0) {
        await imageStore.getTagList({ page_size: 100 });
      }
    }
  }
);

// 提交表单
async function handleSubmit() {
  try {
    await formRef.value?.validate();
    
    if (!props.imageInfo?.id) {
      message.error('图片信息无效');
      return;
    }
    
    loading.value = true;
    
    // 调用更新接口
    const updateParams: ImageUpdateParams = {
      original_name: formData.value.original_name,
      description: formData.value.description,
      tag_ids: formData.value.tag_ids.length > 0 ? formData.value.tag_ids : undefined,
    };

    const updatedImage = await imageStore.updateImage(props.imageInfo.id, updateParams);
    
    message.success('图片信息更新成功');
    emit('edit-success', updatedImage);
    modalVisible.value = false;
  } catch (error) {
    if (error?.errorFields) {
      // 表单验证错误，不显示消息
      return;
    }
    message.error('图片信息更新失败');
    console.error('更新失败:', error);
  } finally {
    loading.value = false;
  }
}

// 取消编辑
function handleCancel() {
  modalVisible.value = false;
  formRef.value?.resetFields();
}

// 格式化文件大小
function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}
</script>

<template>
  <Modal
    v-model:open="modalVisible"
    title="编辑图片信息"
    width="650px"
    @cancel="handleCancel"
  >
    <div v-if="imageInfo" class="image-edit-container">
      <!-- 图片预览 -->
      <div class="image-preview mb-4">
        <div class="flex gap-4">
          <div class="image-wrapper">
            <Image
              :src="imageInfo.access_url"
              :alt="imageInfo.original_name"
              :width="150"
              :height="120"
              :preview="false"
              class="rounded object-cover"
            />
          </div>
          <div class="image-meta">
            <div class="meta-item">
              <span class="label">文件名：</span>
              <span class="value">{{ imageInfo.new_name }}</span>
            </div>
            <div class="meta-item">
              <span class="label">文件大小：</span>
              <span class="value">{{ formatFileSize(imageInfo.file_size) }}</span>
            </div>
            <div class="meta-item">
              <span class="label">图片尺寸：</span>
              <span class="value">{{ imageInfo.width }} × {{ imageInfo.height }}</span>
            </div>
            <div class="meta-item">
              <span class="label">文件类型：</span>
              <span class="value">{{ imageInfo.file_type?.toUpperCase() }}</span>
            </div>
            <div class="meta-item">
              <span class="label">上传时间：</span>
              <span class="value">{{ new Date(imageInfo.create_time).toLocaleString() }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 编辑表单 -->
      <Form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <Form.Item label="图片名称" name="original_name">
          <Input
            v-model:value="formData.original_name"
            placeholder="请输入图片名称"
            :maxlength="100"
            show-count
          />
        </Form.Item>
        
        <Form.Item label="图片描述" name="description">
          <Input.TextArea
            v-model:value="formData.description"
            placeholder="请输入图片描述（可选）"
            :maxlength="500"
            :rows="3"
            show-count
          />
        </Form.Item>

        <Form.Item label="关联标签" name="tag_ids">
          <Select
            v-model:value="formData.tag_ids"
            mode="multiple"
            placeholder="请选择关联的标签（可选）"
            :options="tagOptions"
            allow-clear
          />
        </Form.Item>
      </Form>
    </div>
    
    <template #footer>
      <Space>
        <Button @click="handleCancel">取消</Button>
        <Button 
          type="primary" 
          :loading="loading"
          @click="handleSubmit"
        >
          保存
        </Button>
      </Space>
    </template>
  </Modal>
</template>

<style scoped>
.image-edit-container {
  padding: 8px 0;
}

.image-preview {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 16px;
  background: #fafafa;
  overflow: hidden;
}

.image-wrapper {
  flex-shrink: 0;
  width: 150px;
  height: 120px;
  overflow: hidden;
  border-radius: 4px;
}

.image-meta {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-left: 16px;
}

.meta-item {
  display: flex;
  align-items: flex-start;
  font-size: 13px;
}

.label {
  font-weight: 500;
  color: #666;
  min-width: 70px;
  flex-shrink: 0;
}

.value {
  color: #333;
  word-break: break-all;
  flex: 1;
}

:deep(.ant-image) {
  display: block;
  width: 100%;
  height: 100%;
}

:deep(.ant-image img) {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .image-preview .flex {
    flex-direction: column;
    gap: 16px;
  }

  .image-wrapper {
    width: 100%;
    height: 200px;
  }

  .image-meta {
    margin-left: 0;
  }
}
</style>
