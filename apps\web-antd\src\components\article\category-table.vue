<script setup lang="ts">
import { Button, Popconfirm, Table, Tag } from 'ant-design-vue';
import { Edit, Trash2 } from 'lucide-vue-next';
import type { ArticleCategory } from '#/types';
import { CATEGORY_WEIGHT_OPTIONS } from '#/types';

defineOptions({
  name: 'CategoryTable',
});

const props = defineProps<{
  categories: ArticleCategory[];
  loading: boolean;
  totalRecords: number;
  currentPage: number;
  pageSize: number;
}>();

const emit = defineEmits<{
  edit: [record: ArticleCategory];
  delete: [id: number];
  pageChange: [page: number, pageSize: number];
}>();

// 表格列配置
const columns = [
  {
    dataIndex: 'name',
    key: 'name',
    title: '分类名称',
    width: 150,
  },
  {
    dataIndex: 'weight',
    key: 'weight',
    title: '分类权重',
    width: 200,
  },
  {
    dataIndex: 'seo_title',
    key: 'seo_title',
    title: 'SEO标题',
    width: 200,
    ellipsis: true,
  },
  {
    dataIndex: 'seo_keywords',
    key: 'seo_keywords',
    title: 'SEO关键字',
    width: 150,
    ellipsis: true,
  },
  {
    dataIndex: 'create_time',
    key: 'create_time',
    title: '创建时间',
    width: 180,
  },
  {
    align: 'center' as const,
    key: 'action',
    title: '操作',
    width: 120,
    fixed: 'right' as const,
  },
];

// 获取分类权重标签
function getCategoryWeightLabel(weight: string) {
  const option = CATEGORY_WEIGHT_OPTIONS.find(item => item.value === weight);
  return option?.label || weight;
}

// 获取分类权重颜色
function getCategoryWeightColor(weight: string) {
  const colors = ['blue', 'green', 'orange', 'red', 'purple'];
  const index = parseInt(weight) - 1;
  return colors[index] || 'default';
}

// 格式化时间
function formatTime(time?: string) {
  if (!time) return '-';
  return new Date(time).toLocaleString('zh-CN');
}

// 处理分页变化
function handlePageChange(page: number, pageSize: number) {
  emit('pageChange', page, pageSize);
}
</script>

<template>
  <Table
    :columns="columns"
    :data-source="categories"
    :loading="loading"
    :pagination="{
      current: currentPage,
      pageSize: pageSize,
      total: totalRecords,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total: number, range: [number, number]) =>
        `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
      pageSizeOptions: ['10', '20', '50', '100'],
    }"
    row-key="id"
    scroll="{ x: 1000 }"
    @change="(pagination: any) => handlePageChange(pagination.current, pagination.pageSize)"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.key === 'weight'">
        <Tag :color="getCategoryWeightColor(record.weight)">
          {{ getCategoryWeightLabel(record.weight) }}
        </Tag>
      </template>
      <template v-else-if="column.key === 'seo_title'">
        <span :title="record.seo_title">{{ record.seo_title || '-' }}</span>
      </template>
      <template v-else-if="column.key === 'seo_keywords'">
        <span :title="record.seo_keywords">{{ record.seo_keywords || '-' }}</span>
      </template>
      <template v-else-if="column.key === 'create_time'">
        {{ formatTime(record.create_time) }}
      </template>
      <template v-else-if="column.key === 'action'">
        <div class="flex items-center justify-center gap-1">
          <Button
            size="small"
            title="编辑"
            type="text"
            @click="() => emit('edit', record)"
          >
            <template #icon>
              <Edit :size="14" />
            </template>
          </Button>
          <Popconfirm
            title="确定要删除这个标签分类吗？"
            ok-text="确定"
            cancel-text="取消"
            @confirm="() => emit('delete', record.id!)"
          >
            <Button
              danger
              size="small"
              title="删除"
              type="text"
            >
              <template #icon>
                <Trash2 :size="14" />
              </template>
            </Button>
          </Popconfirm>
        </div>
      </template>
    </template>
  </Table>
</template>
