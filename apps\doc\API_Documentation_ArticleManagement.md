# 文章管理系统 API 接口文档

## 📋 概述

本文档描述了文章管理系统的 RESTful API 接口，包括文章、文章作者、文章标签、标签分类四个核心模块的完整 CRUD 操作。

### 基础信息
- **API 版本**: v1
- **基础路径**: 
  - 文章管理: `/article`
  - 文章作者: `/article-author`
  - 文章标签: `/article-tag`
  - 标签分类: `/article-category`
- **认证方式**: Bearer Token
- **数据格式**: JSON
- **字符编码**: UTF-8

---

## 🔐 认证说明

所有 API 接口都需要在请求头中包含有效的 Bearer Token：

```http
Authorization: Bearer {your_access_token}
Content-Type: application/json
```

---

## 📊 通用响应格式

所有接口都采用统一的响应格式：

```json
{
  "success": boolean,      // 操作是否成功
  "data": any,            // 返回的数据（可为空）
  "message": string,      // 提示信息
  "status_code": number,  // HTTP状态码，默认200
  "total_records": number // 分页查询时的总记录数
}
```

---

## 📋 数据模型

### 1. 标签分类对象 (ArticleCategory)
```json
{
  "id": 1,
  "category_name": "合作伙伴",
  "category_weight": "1",
  "seo_title": "合作伙伴相关文章",
  "seo_keywords": "合作伙伴,服务商,工厂",
  "seo_description": "合作伙伴相关的文章内容",
  "create_time": "2024-01-01T00:00:00Z",
  "update_time": "2024-01-01T00:00:00Z",
  "creator_id": 1,
  "updater_id": 1
}
```

### 2. 文章作者对象 (ArticleAuthor)
```json
{
  "id": 1,
  "wechat_nickname": "跨境小王",
  "author_name": "王小明",
  "create_time": "2024-01-01T00:00:00Z",
  "update_time": "2024-01-01T00:00:00Z",
  "creator_id": 1,
  "updater_id": 1
}
```

### 3. 文章标签对象 (ArticleTag)
```json
{
  "id": 1,
  "tag_name": "跨境电商",
  "first_letter": "K",
  "category_id": 1,
  "article_count": 15,
  "article_read_count": 1250,
  "create_time": "2024-01-01T00:00:00Z",
  "update_time": "2024-01-01T00:00:00Z",
  "creator_id": 1,
  "updater_id": 1,
  "category": {
    "id": 1,
    "category_name": "文章性质"
  }
}
```

### 4. 文章对象 (Article)
```json
{
  "id": 1,
  "title": "2024年跨境电商趋势分析",
  "style": "富文本",
  "content": "<p>文章内容...</p>",
  "channel": "头条",
  "cover_image": "https://example.com/cover.jpg",
  "summary": "本文分析了2024年跨境电商的发展趋势",
  "share_image": "https://example.com/share.jpg",
  "publish_time": "2024-01-01T00:00:00Z",
  "is_visible": true,
  "seo_title": "2024跨境电商趋势",
  "seo_keywords": "跨境电商,趋势,2024",
  "seo_description": "深度分析2024年跨境电商发展趋势",
  "view_count": 1250,
  "like_count": 85,
  "favorite_count": 32,
  "comment_count": 15,
  "status": "3",
  "author_id": 1,
  "create_time": "2024-01-01T00:00:00Z",
  "update_time": "2024-01-01T00:00:00Z",
  "creator_id": 1,
  "updater_id": 1,
  "author": {
    "id": 1,
    "author_name": "王小明",
    "wechat_nickname": "跨境小王"
  },
  "tags": [
    {
      "id": 1,
      "tag_name": "跨境电商",
      "first_letter": "K"
    }
  ]
}
```

---

## 🏷️ 标签分类管理接口

### 1. 创建标签分类

**接口地址**: `POST /article-category/create`

#### 请求参数
```json
{
  "category_name": "合作伙伴",
  "category_weight": "1",
  "seo_title": "合作伙伴相关文章",
  "seo_keywords": "合作伙伴,服务商,工厂",
  "seo_description": "合作伙伴相关的文章内容"
}
```

**参数说明**:
- `category_name`: 分类名称，必填，长度不超过20字
- `category_weight`: 分类权重，必填，可选值：
  - "1": 合作伙伴（服务商及工厂）
  - "2": 文章性质（实操干货、政策解读、突发新闻、跨境活动等）
  - "3": 服务品类（物流、软件、金融等等）、产业带
  - "4": 国家/地区
  - "5": 平台/独立站
- `seo_title`: SEO标题，可选，长度不超过100字
- `seo_keywords`: SEO关键字，可选，长度不超过100字
- `seo_description`: SEO描述，可选，长度不超过300字

#### 响应示例
```json
{
  "success": true,
  "data": {
    "id": 1,
    "category_name": "合作伙伴",
    "category_weight": "1",
    "seo_title": "合作伙伴相关文章",
    "seo_keywords": "合作伙伴,服务商,工厂",
    "seo_description": "合作伙伴相关的文章内容",
    "create_time": "2024-01-01T00:00:00Z",
    "update_time": "2024-01-01T00:00:00Z",
    "creator_id": 1,
    "updater_id": 1
  },
  "message": "标签分类创建成功"
}
```

### 2. 获取标签分类列表

**接口地址**: `GET /article-category/list`

#### 请求参数
- `page`: 页码，默认1
- `page_size`: 每页条数，默认10，最大100
- `category_name`: 分类名称模糊查询，可选
- `category_weight`: 分类权重精确查询，可选

#### 调用示例
```
GET /article-category/list?page=1&page_size=10&category_name=合作&category_weight=1
```

#### 响应示例
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "category_name": "合作伙伴",
      "category_weight": "1",
      "seo_title": "合作伙伴相关文章",
      "seo_keywords": "合作伙伴,服务商,工厂",
      "seo_description": "合作伙伴相关的文章内容",
      "create_time": "2024-01-01T00:00:00Z",
      "update_time": "2024-01-01T00:00:00Z",
      "creator_id": 1,
      "updater_id": 1
    }
  ],
  "total_records": 1,
  "message": "获取标签分类列表成功"
}
```

### 3. 获取单个标签分类

**接口地址**: `GET /article-category/{category_id}`

#### 响应示例
```json
{
  "success": true,
  "data": {
    "id": 1,
    "category_name": "合作伙伴",
    "category_weight": "1",
    "seo_title": "合作伙伴相关文章",
    "seo_keywords": "合作伙伴,服务商,工厂",
    "seo_description": "合作伙伴相关的文章内容",
    "create_time": "2024-01-01T00:00:00Z",
    "update_time": "2024-01-01T00:00:00Z",
    "creator_id": 1,
    "updater_id": 1
  },
  "message": "获取标签分类详情成功"
}
```

### 4. 更新标签分类

**接口地址**: `PUT /article-category/{category_id}`

#### 请求参数
```json
{
  "category_name": "合作伙伴更新",
  "seo_title": "更新后的SEO标题"
}
```

### 5. 删除标签分类

**接口地址**: `DELETE /article-category/{category_id}`

#### 响应示例
```json
{
  "success": true,
  "message": "标签分类删除成功"
}
```

---

## 👤 文章作者管理接口

### 1. 创建文章作者

**接口地址**: `POST /article-author/create`

#### 请求参数
```json
{
  "wechat_nickname": "跨境小王",
  "author_name": "王小明"
}
```

**参数说明**:
- `wechat_nickname`: 微信昵称，必填，长度不超过30字
- `author_name`: 作者名，必填，长度不超过30字

#### 响应示例
```json
{
  "success": true,
  "data": {
    "id": 1,
    "wechat_nickname": "跨境小王",
    "author_name": "王小明",
    "create_time": "2024-01-01T00:00:00Z",
    "update_time": "2024-01-01T00:00:00Z",
    "creator_id": 1,
    "updater_id": 1
  },
  "message": "文章作者创建成功"
}
```

### 2. 获取文章作者列表

**接口地址**: `GET /article-author/list`

#### 请求参数
- `page`: 页码，默认1
- `page_size`: 每页条数，默认10，最大100
- `wechat_nickname`: 微信昵称模糊查询，可选
- `author_name`: 作者名模糊查询，可选

#### 调用示例
```
GET /article-author/list?page=1&page_size=10&author_name=王
```

#### 响应示例
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "wechat_nickname": "跨境小王",
      "author_name": "王小明",
      "create_time": "2024-01-01T00:00:00Z",
      "update_time": "2024-01-01T00:00:00Z",
      "creator_id": 1,
      "updater_id": 1
    }
  ],
  "total_records": 1,
  "message": "获取文章作者列表成功"
}
```

### 3. 获取单个文章作者

**接口地址**: `GET /article-author/{author_id}`

### 4. 更新文章作者

**接口地址**: `PUT /article-author/{author_id}`

### 5. 删除文章作者

**接口地址**: `DELETE /article-author/{author_id}`

---

## 🏷️ 文章标签管理接口

### 1. 创建文章标签

**接口地址**: `POST /article-tag/create`

#### 请求参数
```json
{
  "tag_name": "跨境电商",
  "category_id": 1
}
```

**参数说明**:
- `tag_name`: 标签名，必填，长度不超过20字
- `category_id`: 关联标签分类ID，必填
- `first_letter`: 首字母，系统自动生成，无需传入

#### 响应示例
```json
{
  "success": true,
  "data": {
    "id": 1,
    "tag_name": "跨境电商",
    "first_letter": "K",
    "category_id": 1,
    "article_count": 0,
    "article_read_count": 0,
    "create_time": "2024-01-01T00:00:00Z",
    "update_time": "2024-01-01T00:00:00Z",
    "creator_id": 1,
    "updater_id": 1,
    "category": {
      "id": 1,
      "category_name": "文章性质"
    }
  },
  "message": "文章标签创建成功"
}
```

### 2. 获取文章标签列表

**接口地址**: `GET /article-tag/list`

#### 请求参数
- `page`: 页码，默认1
- `page_size`: 每页条数，默认10，最大100
- `tag_name`: 标签名模糊查询，可选
- `first_letter`: 首字母精确查询，可选
- `category_id`: 标签分类精确查询，可选
- `category_weight`: 分类权重精确查询，可选

#### 调用示例
```
GET /article-tag/list?page=1&page_size=10&tag_name=跨境&first_letter=K&category_id=1
```

#### 响应示例
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "tag_name": "跨境电商",
      "first_letter": "K",
      "category_id": 1,
      "article_count": 15,
      "article_read_count": 1250,
      "create_time": "2024-01-01T00:00:00Z",
      "update_time": "2024-01-01T00:00:00Z",
      "creator_id": 1,
      "updater_id": 1,
      "category": {
        "id": 1,
        "category_name": "文章性质"
      }
    }
  ],
  "total_records": 1,
  "message": "获取文章标签列表成功"
}
```

### 3. 获取单个文章标签

**接口地址**: `GET /article-tag/{tag_id}`

### 4. 更新文章标签

**接口地址**: `PUT /article-tag/{tag_id}`

### 5. 删除文章标签

**接口地址**: `DELETE /article-tag/{tag_id}`

---

## 📝 文章管理接口

### 1. 创建文章

**接口地址**: `POST /article/create`

#### 请求参数
```json
{
  "title": "2024年跨境电商趋势分析",
  "style": "富文本",
  "content": "<p>文章内容...</p>",
  "channel": "头条",
  "cover_image": "https://example.com/cover.jpg",
  "summary": "本文分析了2024年跨境电商的发展趋势",
  "share_image": "https://example.com/share.jpg",
  "publish_time": "2024-01-01T00:00:00Z",
  "is_visible": true,
  "seo_title": "2024跨境电商趋势",
  "seo_keywords": "跨境电商,趋势,2024",
  "seo_description": "深度分析2024年跨境电商发展趋势",
  "status": "1",
  "author_id": 1,
  "tag_ids": [1, 2, 3]
}
```

**参数说明**:
- `title`: 标题，必填，长度不超过100字
- `style`: 风格，必填，可选值："富文本"、"markdown"
- `content`: 文章内容，必填，长文本
- `channel`: 发布频道，必填，可选值："头条"、"百科"、"快讯"
- `cover_image`: 文章封面，可选，图片URL
- `summary`: 简介，可选，长度不超过300字
- `share_image`: 分享封面，可选，图片URL
- `publish_time`: 发表时间，必填，ISO格式时间
- `is_visible`: 是否展示，必填，布尔值，默认true
- `seo_title`: SEO标题，可选，长度不超过100字
- `seo_keywords`: SEO关键字，可选，长度不超过100字
- `seo_description`: SEO描述，可选，长度不超过300字
- `status`: 状态，必填，可选值："0"(草稿)、"1"(待审核)、"2"(审核中)、"3"(已发布)
- `author_id`: 关联作者ID，必填
- `tag_ids`: 关联标签ID列表，必填，数组格式

#### 响应示例
```json
{
  "success": true,
  "data": {
    "id": 1,
    "title": "2024年跨境电商趋势分析",
    "style": "富文本",
    "content": "<p>文章内容...</p>",
    "channel": "头条",
    "cover_image": "https://example.com/cover.jpg",
    "summary": "本文分析了2024年跨境电商的发展趋势",
    "share_image": "https://example.com/share.jpg",
    "publish_time": "2024-01-01T00:00:00Z",
    "is_visible": true,
    "seo_title": "2024跨境电商趋势",
    "seo_keywords": "跨境电商,趋势,2024",
    "seo_description": "深度分析2024年跨境电商发展趋势",
    "view_count": 0,
    "like_count": 0,
    "favorite_count": 0,
    "comment_count": 0,
    "status": "1",
    "author_id": 1,
    "create_time": "2024-01-01T00:00:00Z",
    "update_time": "2024-01-01T00:00:00Z",
    "creator_id": 1,
    "updater_id": 1,
    "author": {
      "id": 1,
      "author_name": "王小明",
      "wechat_nickname": "跨境小王"
    },
    "tags": [
      {
        "id": 1,
        "tag_name": "跨境电商",
        "first_letter": "K"
      }
    ]
  },
  "message": "文章创建成功"
}
```

### 2. 获取文章列表

**接口地址**: `GET /article/list`

#### 请求参数
- `page`: 页码，默认1
- `page_size`: 每页条数，默认10，最大100
- `title`: 标题模糊查询，可选
- `author_name`: 作者名模糊查询，可选
- `tag_id`: 标签精确查询，可选
- `channel`: 发布频道精确查询，可选
- `status`: 状态精确查询，可选
- `publish_start`: 发布时间开始，可选，ISO格式
- `publish_end`: 发布时间结束，可选，ISO格式

#### 调用示例
```
GET /article/list?page=1&page_size=10&title=跨境&author_name=王&tag_id=1&channel=头条&status=3&publish_start=2024-01-01T00:00:00Z&publish_end=2024-12-31T23:59:59Z
```

#### 响应示例
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "title": "2024年跨境电商趋势分析",
      "style": "富文本",
      "content": "<p>文章内容...</p>",
      "channel": "头条",
      "cover_image": "https://example.com/cover.jpg",
      "summary": "本文分析了2024年跨境电商的发展趋势",
      "share_image": "https://example.com/share.jpg",
      "publish_time": "2024-01-01T00:00:00Z",
      "is_visible": true,
      "seo_title": "2024跨境电商趋势",
      "seo_keywords": "跨境电商,趋势,2024",
      "seo_description": "深度分析2024年跨境电商发展趋势",
      "view_count": 1250,
      "like_count": 85,
      "favorite_count": 32,
      "comment_count": 15,
      "status": "3",
      "author_id": 1,
      "create_time": "2024-01-01T00:00:00Z",
      "update_time": "2024-01-01T00:00:00Z",
      "creator_id": 1,
      "updater_id": 1,
      "author": {
        "id": 1,
        "author_name": "王小明",
        "wechat_nickname": "跨境小王"
      },
      "tags": [
        {
          "id": 1,
          "tag_name": "跨境电商",
          "first_letter": "K"
        }
      ]
    }
  ],
  "total_records": 1,
  "message": "获取文章列表成功"
}
```

### 3. 获取单个文章详情

**接口地址**: `GET /article/{article_id}`

**说明**: 访问此接口会自动增加文章的浏览数

#### 响应示例
```json
{
  "success": true,
  "data": {
    "id": 1,
    "title": "2024年跨境电商趋势分析",
    "style": "富文本",
    "content": "<p>文章内容...</p>",
    "channel": "头条",
    "cover_image": "https://example.com/cover.jpg",
    "summary": "本文分析了2024年跨境电商的发展趋势",
    "share_image": "https://example.com/share.jpg",
    "publish_time": "2024-01-01T00:00:00Z",
    "is_visible": true,
    "seo_title": "2024跨境电商趋势",
    "seo_keywords": "跨境电商,趋势,2024",
    "seo_description": "深度分析2024年跨境电商发展趋势",
    "view_count": 1251,
    "like_count": 85,
    "favorite_count": 32,
    "comment_count": 15,
    "status": "3",
    "author_id": 1,
    "create_time": "2024-01-01T00:00:00Z",
    "update_time": "2024-01-01T00:00:00Z",
    "creator_id": 1,
    "updater_id": 1,
    "author": {
      "id": 1,
      "author_name": "王小明",
      "wechat_nickname": "跨境小王"
    },
    "tags": [
      {
        "id": 1,
        "tag_name": "跨境电商",
        "first_letter": "K"
      }
    ]
  },
  "message": "获取文章详情成功"
}
```

### 4. 更新文章

**接口地址**: `PUT /article/{article_id}`

#### 请求参数
```json
{
  "title": "2024年跨境电商趋势分析（更新版）",
  "summary": "更新后的文章简介",
  "tag_ids": [1, 2, 4]
}
```

**说明**: 只需传入需要更新的字段，支持部分更新

### 5. 删除文章

**接口地址**: `DELETE /article/{article_id}`

#### 响应示例
```json
{
  "success": true,
  "message": "文章删除成功"
}
```

### 6. 文章点赞

**接口地址**: `POST /article/{article_id}/like`

#### 响应示例
```json
{
  "success": true,
  "data": {
    "like_count": 86
  },
  "message": "点赞成功"
}
```

### 7. 文章收藏

**接口地址**: `POST /article/{article_id}/favorite`

#### 响应示例
```json
{
  "success": true,
  "data": {
    "favorite_count": 33
  },
  "message": "收藏成功"
}
```

### 8. 更新文章状态

**接口地址**: `PUT /article/{article_id}/status`

#### 请求参数
- `status`: 状态值，必填，可选值："0"(草稿)、"1"(待审核)、"2"(审核中)、"3"(已发布)

#### 调用示例
```
PUT /article/1/status?status=3
```

#### 响应示例
```json
{
  "success": true,
  "data": {
    "id": 1,
    "title": "2024年跨境电商趋势分析",
    "status": "3",
    // ... 其他字段
  },
  "message": "文章状态已更新为：已发布"
}
```

---

## 🛠️ Vue.js 前端集成指南

### 1. 基础配置

```javascript
// api/config.js
export const API_BASE_URL = 'https://your-api-domain.com'
export const API_ENDPOINTS = {
  ARTICLE: '/article',
  ARTICLE_AUTHOR: '/article-author',
  ARTICLE_TAG: '/article-tag',
  ARTICLE_CATEGORY: '/article-category'
}

// utils/request.js
import axios from 'axios'
import { API_BASE_URL } from '@/api/config'

const request = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  config => {
    const token = localStorage.getItem('access_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  response => {
    const { data } = response
    if (data.success) {
      return data
    } else {
      throw new Error(data.message || '请求失败')
    }
  },
  error => {
    console.error('API Error:', error)
    throw error
  }
)

export default request
```

### 2. API 服务封装

```javascript
// api/articleService.js
import request from '@/utils/request'
import { API_ENDPOINTS } from './config'

export const articleService = {
  // 文章管理
  createArticle: (data) => request.post(`${API_ENDPOINTS.ARTICLE}/create`, data),
  getArticleList: (params) => request.get(`${API_ENDPOINTS.ARTICLE}/list`, { params }),
  getArticleDetail: (id) => request.get(`${API_ENDPOINTS.ARTICLE}/${id}`),
  updateArticle: (id, data) => request.put(`${API_ENDPOINTS.ARTICLE}/${id}`, data),
  deleteArticle: (id) => request.delete(`${API_ENDPOINTS.ARTICLE}/${id}`),
  likeArticle: (id) => request.post(`${API_ENDPOINTS.ARTICLE}/${id}/like`),
  favoriteArticle: (id) => request.post(`${API_ENDPOINTS.ARTICLE}/${id}/favorite`),
  updateArticleStatus: (id, status) => request.put(`${API_ENDPOINTS.ARTICLE}/${id}/status?status=${status}`),

  // 文章作者管理
  createAuthor: (data) => request.post(`${API_ENDPOINTS.ARTICLE_AUTHOR}/create`, data),
  getAuthorList: (params) => request.get(`${API_ENDPOINTS.ARTICLE_AUTHOR}/list`, { params }),
  getAuthorDetail: (id) => request.get(`${API_ENDPOINTS.ARTICLE_AUTHOR}/${id}`),
  updateAuthor: (id, data) => request.put(`${API_ENDPOINTS.ARTICLE_AUTHOR}/${id}`, data),
  deleteAuthor: (
</augment_code_snippet>