<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { Modal, Form, Input, Switch, Button, message } from 'ant-design-vue';
import { Plus, Edit, Trash2 } from 'lucide-vue-next';
import type { SiteConfig } from '#/types';

defineOptions({
  name: 'HeaderLinkForm',
});

const props = defineProps<{
  visible: boolean;
  editingRecord?: SiteConfig | null;
}>();

const emit = defineEmits<{
  'update:visible': [visible: boolean];
  cancel: [];
  confirm: [formData: any];
  'add-link': [];
  'edit-link': [index: number];
  'delete-link': [index: number];
  'get-current-form-data': [formData: any]; // 新增事件，用于获取当前表单数据
}>();

// 表单引用
const addFormRef = ref();

// 表单数据
const addForm = ref({
  title: '',
  link_url: '',
  prompt_text: '',
  is_enabled: true,
  partner_code: '',
  links: [] as Array<{ name: string; url: string }>
});

// 表单验证规则
const addFormRules = {
  title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
  link_url: [{ required: true, message: '请输入链接地址', trigger: 'blur' }],
  prompt_text: [{ required: true, message: '请输入提示文本', trigger: 'blur' }],
  partner_code: [{ required: true, message: '请输入合作伙伴代码', trigger: 'blur' }],
};

// 计算属性：弹窗标题
const modalTitle = computed(() => {
  return props.editingRecord?.id ? '编辑链接' : '新增链接';
});

// 重置表单
function resetAddForm() {
  addForm.value = {
    title: '',
    link_url: '',
    prompt_text: '',
    is_enabled: true,
    partner_code: '',
    links: []
  };
  addFormRef.value?.resetFields();
}

// 监听编辑记录变化
watch(() => props.editingRecord, (newRecord) => {
  if (newRecord) {
    addForm.value = {
      title: newRecord.title || '',
      link_url: newRecord.link_url || '',
      prompt_text: newRecord.prompt_text || '',
      is_enabled: newRecord.is_enabled ?? true,
      partner_code: newRecord.partner_code || '',
      links: newRecord.link_groups?.map(group => ({
        name: group.name,
        url: group.url
      })) || []
    };
  } else {
    resetAddForm();
  }
}, { immediate: true });

// 监听visible属性变化
watch(() => props.visible, (newVal) => {
  if (!newVal) {
    // 当弹窗关闭时，重置表单
    resetAddForm();
  }
});

// 确认提交
async function handleConfirm() {
  try {
    await addFormRef.value?.validate();
    
    // 转换链接组数据格式
    const formData = { ...addForm.value };
    
    // 只有在编辑模式且有有效ID时才包含ID
    if (props.editingRecord?.id) {
      formData.id = props.editingRecord.id;
    } else {
      // 新增模式下，确保不包含ID字段
      delete formData.id;
    }
    
    emit('confirm', formData);
  } catch (error) {
    console.error('表单验证失败:', error);
  }
}

// 取消
function handleCancel() {
  emit('update:visible', false);
  emit('cancel');
}

// 处理弹窗关闭
function handleModalCancel() {
  emit('update:visible', false);
  emit('cancel');
}

// 添加链接
function handleAddLink() {
  // 发送当前表单数据给父组件
  emit('get-current-form-data', { ...addForm.value });
  emit('add-link');
}

// 编辑链接
function handleEditLink(index: number) {
  // 发送当前表单数据给父组件
  emit('get-current-form-data', { ...addForm.value });
  emit('edit-link', index);
}

// 删除链接
function handleDeleteLink(index: number) {
  // 直接从本地表单数据中删除
  addForm.value.links.splice(index, 1);
}

// 添加新的方法来更新链接组
function updateLinks(newLinks: Array<{ name: string; url: string }>) {
  addForm.value.links = [...newLinks];
}

// 暴露方法给父组件
defineExpose({
  updateLinks
});
</script>

<template>
  <Modal
    :open="visible"
    :title="modalTitle"
    :width="600"
    :destroy-on-close="true"
    :mask-closable="false"
    @cancel="handleModalCancel"
  >
    <Form
      ref="addFormRef"
      :model="addForm"
      :rules="addFormRules"
      layout="vertical"
    >
      <Form.Item label="标题" name="title">
        <Input v-model:value="addForm.title" placeholder="请输入标题" />
      </Form.Item>
      
      <Form.Item label="链接地址" name="link_url">
        <Input v-model:value="addForm.link_url" placeholder="请输入链接地址" />
      </Form.Item>
      
      <Form.Item label="提示文本" name="prompt_text">
        <Input v-model:value="addForm.prompt_text" placeholder="请输入提示文本" />
      </Form.Item>
      
      <Form.Item label="合作伙伴代码" name="partner_code">
        <Input v-model:value="addForm.partner_code" placeholder="请输入合作伙伴代码" />
      </Form.Item>
      
      <Form.Item label="启用状态">
        <Switch v-model:checked="addForm.is_enabled" />
      </Form.Item>
      
      <!-- 链接组管理 -->
      <Form.Item label="链接组">
        <div class="space-y-2">
          <div
            v-for="(link, index) in addForm.links"
            :key="index"
            class="flex items-center gap-2 p-2 border rounded"
          >
            <div class="flex-1">
              <div class="text-sm font-medium">{{ link.name }}</div>
              <div class="text-xs text-gray-500">{{ link.url }}</div>
            </div>
            <Button
              size="small"
              type="text"
              @click="handleEditLink(index)"
            >
              <template #icon>
                <Edit :size="14" />
              </template>
            </Button>
            <Button
              size="small"
              type="text"
              danger
              @click="handleDeleteLink(index)"
            >
              <template #icon>
                <Trash2 :size="14" />
              </template>
            </Button>
          </div>
          
          <Button
            type="dashed"
            block
            class="flex items-center justify-center"
            @click="handleAddLink"
          >
            <template #icon>
              <Plus :size="14" />
            </template>
            添加链接
          </Button>
        </div>
      </Form.Item>
    </Form>
    
    <template #footer>
      <div class="flex justify-end gap-2">
        <Button @click="handleCancel">取消</Button>
        <Button type="primary" @click="handleConfirm">确认</Button>
      </div>
    </template>
  </Modal>
</template>






