// 标签分类管理 store
import { ref } from 'vue';
import { defineStore } from 'pinia';
import { requestClient } from '#/api/request';

// types
import type {
  ArticleCategory,
  ArticleCategoryFormData,
  ArticleCategoryApiData,
  ArticleCategoryListParams,
  StanderResult,
} from '#/types';

// API响应类型
interface BatchDeleteResponse {
  deleted_count: number;
}

// API函数
function _createArticleCategory(params: ArticleCategoryApiData) {
  return requestClient.post<StanderResult<ArticleCategory>>(
    'article-category/create',
    params,
  );
}

function _getArticleCategoryList(params: ArticleCategoryListParams = {}) {
  return requestClient.get<
    { total_records: number } & StanderResult<ArticleCategory[]>
  >('article-category/list', { params });
}

function _getArticleCategoryDetail(id: number) {
  return requestClient.get<StanderResult<ArticleCategory>>(
    `article-category/${id}`,
  );
}

function _updateArticleCategory(id: number, params: Partial<ArticleCategoryApiData>) {
  return requestClient.put<StanderResult<ArticleCategory>>(
    `article-category/${id}`,
    params,
  );
}

function _deleteArticleCategory(id: number) {
  return requestClient.delete<StanderResult<null>>(
    `article-category/${id}`,
  );
}

function _batchDeleteArticleCategory(ids: number[]) {
  return requestClient.delete<StanderResult<BatchDeleteResponse>>(
    'article-category/batch-delete',
    { data: { ids } },
  );
}

// store
export const useArticleCategoryStore = defineStore('article-category-store', () => {
  const loading = ref<boolean>(false);
  const categories = ref<ArticleCategory[]>([]);
  const totalRecords = ref<number>(0);

  // 创建标签分类
  async function addArticleCategory(params: ArticleCategoryFormData) {
    try {
      loading.value = true;
      // 直接使用表单数据，字段名已经统一
      const apiData: ArticleCategoryApiData = {
        name: params.name,
        weight: params.weight,
        seo_title: params.seo_title,
        seo_keywords: params.seo_keywords,
        seo_description: params.seo_description,
      };
      const res = await _createArticleCategory(apiData);
      if (res.success) {
        // 创建成功后刷新列表
        await getArticleCategoryList();
        return res.data;
      }
      throw new Error(res.message || '创建失败');
    } finally {
      loading.value = false;
    }
  }

  // 获取标签分类列表
  async function getArticleCategoryList(params: ArticleCategoryListParams = {}) {
    try {
      loading.value = true;
      // 直接使用搜索参数，字段名已经统一
      const apiParams: any = {
        page: params.page,
        page_size: params.page_size,
      };
      if (params.name) apiParams.name = params.name;
      if (params.weight) apiParams.weight = params.weight;

      const res = await _getArticleCategoryList(apiParams);
      if (res.success) {
        categories.value = res.data || [];
        totalRecords.value = res.total_records || 0;
        return res.data;
      }
      throw new Error(res.message || '获取列表失败');
    } finally {
      loading.value = false;
    }
  }

  // 获取标签分类详情
  async function getArticleCategoryDetail(id: number) {
    try {
      loading.value = true;
      const res = await _getArticleCategoryDetail(id);
      if (res.success) {
        return res.data;
      }
      throw new Error(res.message || '获取详情失败');
    } finally {
      loading.value = false;
    }
  }

  // 更新标签分类
  async function updateArticleCategory(params: Partial<ArticleCategory & ArticleCategoryFormData>) {
    try {
      loading.value = true;
      if (!params.id) {
        throw new Error('更新失败：缺少ID');
      }

      const id = params.id;

      // 直接使用表单数据，字段名已经统一
      const apiData: Partial<ArticleCategoryApiData> = {};
      if (params.name !== undefined) apiData.name = params.name;
      if (params.weight !== undefined) apiData.weight = params.weight;
      if (params.seo_title !== undefined) apiData.seo_title = params.seo_title;
      if (params.seo_keywords !== undefined) apiData.seo_keywords = params.seo_keywords;
      if (params.seo_description !== undefined) apiData.seo_description = params.seo_description;

      const res = await _updateArticleCategory(id, apiData);
      if (res.success) {
        // 更新成功后刷新列表
        await getArticleCategoryList();
        return res.data;
      }
      throw new Error(res.message || '更新失败');
    } finally {
      loading.value = false;
    }
  }

  // 删除标签分类
  async function deleteArticleCategory(id: number) {
    try {
      loading.value = true;
      const res = await _deleteArticleCategory(id);
      if (res.success) {
        // 删除成功后刷新列表
        await getArticleCategoryList();
        return true;
      }
      throw new Error(res.message || '删除失败');
    } finally {
      loading.value = false;
    }
  }

  // 批量删除标签分类
  async function batchDeleteArticleCategory(ids: number[]) {
    try {
      loading.value = true;
      const res = await _batchDeleteArticleCategory(ids);
      if (res.success) {
        // 删除成功后刷新列表
        await getArticleCategoryList();
        return res.data;
      }
      throw new Error(res.message || '批量删除失败');
    } finally {
      loading.value = false;
    }
  }

  return {
    loading,
    categories,
    totalRecords,
    addArticleCategory,
    getArticleCategoryList,
    getArticleCategoryDetail,
    updateArticleCategory,
    deleteArticleCategory,
    batchDeleteArticleCategory,
  };
});
