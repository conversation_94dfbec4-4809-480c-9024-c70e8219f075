// 图片管理 store
import { ref } from 'vue';
import { defineStore } from 'pinia';
import { requestClient } from '#/api/request';

// types
import type {
  ImageInfo,
  ImageTag,
  ImageUploadParams,
  ImageUpdateParams,
  ImageListParams,
  TagCreateParams,
  TagUpdateParams,
  TagListParams,
  ApiResponse,
} from '#/types/image';

// API函数 - 图片管理
function _uploadImage(params: FormData) {
  return requestClient.post<ApiResponse<ImageInfo>>(
    'image/upload',
    params,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }
  );
}

function _getImageList(params: ImageListParams = {}) {
  return requestClient.get<ApiResponse<ImageInfo[]> & { total_records: number }>(
    'image/list',
    { params }
  );
}

function _deleteImage(id: number) {
  return requestClient.delete<ApiResponse<null>>(
    `image/${id}`
  );
}

function _batchDeleteImages(ids: number[]) {
  return requestClient.post<ApiResponse<{ deleted_count: number }>>(
    'image/batch-delete',
    ids
  );
}

// API函数 - 标签管理
function _createTag(params: TagCreateParams) {
  return requestClient.post<ApiResponse<ImageTag>>(
    'image-tag/create',
    params
  );
}

function _getTagList(params: TagListParams = {}) {
  return requestClient.get<ApiResponse<ImageTag[]> & { total_records: number }>(
    'image-tag/list',
    { params }
  );
}

function _updateTag(id: number, params: TagUpdateParams) {
  return requestClient.put<ApiResponse<ImageTag>>(
    `image-tag/update/${id}`,
    params
  );
}

function _deleteTag(id: number) {
  return requestClient.delete<ApiResponse<null>>(
    `image-tag/delete/${id}`
  );
}

function _updateImage(id: number, params: ImageUpdateParams) {
  return requestClient.put<ApiResponse<ImageInfo>>(
    `image/${id}`,
    params
  );
}

// store
export const useImageStore = defineStore('image-store', () => {
  const loading = ref<boolean>(false);
  const images = ref<ImageInfo[]>([]);
  const totalRecords = ref<number>(0);
  const tags = ref<ImageTag[]>([]);
  const totalTags = ref<number>(0);

  // 上传图片
  async function uploadImage(params: ImageUploadParams) {
    try {
      loading.value = true;
      
      const formData = new FormData();
      formData.append('file', params.file);
      if (params.original_name) formData.append('original_name', params.original_name);
      if (params.description) formData.append('description', params.description);
      if (params.category) formData.append('category', params.category);
      if (params.is_public !== undefined) formData.append('is_public', String(params.is_public));
      if (params.tag_ids && params.tag_ids.trim()) formData.append('tag_ids', params.tag_ids);
      
      const res = await _uploadImage(formData);
      if (res.success) {
        return res.data;
      }
      throw new Error(res.message || '上传失败');
    } finally {
      loading.value = false;
    }
  }

  // 获取图片列表
  async function getImageList(params: ImageListParams = {}) {
    try {
      loading.value = true;
      const res = await _getImageList(params);
      if (res.success) {
        images.value = res.data || [];
        totalRecords.value = res.total_records || 0;
        return res.data;
      }
      throw new Error(res.message || '获取图片列表失败');
    } finally {
      loading.value = false;
    }
  }

  // 删除图片
  async function deleteImage(id: number) {
    try {
      loading.value = true;
      const res = await _deleteImage(id);
      if (res.success) {
        // 删除成功后刷新列表
        await getImageList();
        return true;
      }
      throw new Error(res.message || '删除失败');
    } finally {
      loading.value = false;
    }
  }

  // 批量删除图片
  async function batchDeleteImages(ids: number[]) {
    try {
      loading.value = true;
      const res = await _batchDeleteImages(ids);
      if (res.success) {
        // 删除成功后刷新列表
        await getImageList();
        return res.data;
      }
      throw new Error(res.message || '批量删除失败');
    } finally {
      loading.value = false;
    }
  }

  // 创建标签
  async function createTag(params: TagCreateParams) {
    try {
      loading.value = true;
      const res = await _createTag(params);
      if (res.success) {
        // 创建成功后刷新标签列表
        await getTagList();
        return res.data;
      }
      throw new Error(res.message || '创建标签失败');
    } finally {
      loading.value = false;
    }
  }

  // 获取标签列表
  async function getTagList(params: TagListParams = {}) {
    try {
      const res = await _getTagList(params);
      if (res.success) {
        tags.value = res.data || [];
        totalTags.value = res.total_records || 0;
        return res.data;
      }
      throw new Error(res.message || '获取标签列表失败');
    } catch (error) {
      throw error;
    }
  }

  // 更新标签
  async function updateTag(id: number, params: TagUpdateParams) {
    try {
      loading.value = true;
      const res = await _updateTag(id, params);
      if (res.success) {
        // 更新成功后刷新标签列表
        await getTagList();
        return res.data;
      }
      throw new Error(res.message || '更新标签失败');
    } finally {
      loading.value = false;
    }
  }

  // 删除标签
  async function deleteTag(id: number) {
    try {
      loading.value = true;
      const res = await _deleteTag(id);
      if (res.success) {
        // 删除成功后刷新标签列表
        await getTagList();
        return true;
      }
      throw new Error(res.message || '删除标签失败');
    } finally {
      loading.value = false;
    }
  }

  // 更新图片信息
  async function updateImage(id: number, params: ImageUpdateParams) {
    try {
      loading.value = true;
      const res = await _updateImage(id, params);
      if (res.success) {
        // 更新成功后刷新图片列表
        await getImageList();
        return res.data;
      }
      throw new Error(res.message || '更新图片信息失败');
    } finally {
      loading.value = false;
    }
  }

  return {
    loading,
    images,
    totalRecords,
    tags,
    totalTags,
    uploadImage,
    getImageList,
    updateImage,
    deleteImage,
    batchDeleteImages,
    createTag,
    getTagList,
    updateTag,
    deleteTag,
  };
});
