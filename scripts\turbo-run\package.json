{"name": "@vben/turbo-run", "version": "5.3.0-beta.2", "private": true, "license": "MIT", "type": "module", "scripts": {"stub": "pnpm unbuild --stub"}, "files": ["dist"], "bin": {"turbo-run": "./bin/turbo-run.mjs"}, "main": "./dist/index.mjs", "module": "./dist/index.mjs", "exports": {".": {"default": "./dist/index.mjs"}, "./package.json": "./package.json"}, "dependencies": {"@clack/prompts": "^0.7.0", "@vben/node-utils": "workspace:*", "cac": "^6.7.14"}}