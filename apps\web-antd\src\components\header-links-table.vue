<script setup lang="ts">
import { Button, message, Modal, Popconfirm, Switch, Table, Tag } from 'ant-design-vue';
import { Copy, Edit, Trash2 } from 'lucide-vue-next';
import type { SiteConfig } from '#/types';

defineOptions({
  name: 'HeaderLinksTable',
});

const props = defineProps<{
  siteConfigs: SiteConfig[];
  loading: boolean;
}>();

const emit = defineEmits<{
  edit: [record: SiteConfig];
  delete: [id: string];
  toggleStatus: [record: SiteConfig];
}>();

// 表格列配置
const columns = [
  {
    dataIndex: 'title',
    key: 'title',
    title: '链接名称',
    width: 150,
  },
  {
    dataIndex: 'link_url',
    key: 'link_url',
    title: '链接地址',
    width: 200,
  },
  {
    dataIndex: 'prompt_text',
    key: 'prompt_text',
    title: '提示文本',
    width: 150,
  },
  {
    dataIndex: 'is_new_page',
    key: 'is_new_page',
    title: '新页面打开',
    width: 100,
  },
  {
    dataIndex: 'is_enabled',
    key: 'is_enabled',
    title: '启用状态',
    width: 100,
  },
  {
    align: 'center' as const,
    key: 'action',
    title: '操作',
    width: 150,
  },
];

// 复制
function handleCopy(record: SiteConfig) {
  Modal.info({
    content: `复制链接"${record.title}"功能正在开发中，请稍后再试。`,
    okText: '知道了',
    title: '复制链接',
  });
}
</script>

<template>
  <Table
    :columns="columns"
    :data-source="siteConfigs"
    :loading="loading"
    :pagination="{
      pageSize: 10,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total: number, range: [number, number]) =>
        `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
    }"
    row-key="id"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.key === 'is_new_page'">
        <Tag :color="record.is_new_page ? 'success' : 'default'">
          {{ record.is_new_page ? '新窗口' : '当前窗口' }}
        </Tag>
      </template>
      <template v-else-if="column.key === 'is_enabled'">
        <Switch
          :checked="record.is_enabled"
          :loading="loading"
          @change="() => emit('toggleStatus', record)"
        />
      </template>
      <template v-else-if="column.key === 'action'">
        <div class="flex items-center justify-center gap-1">
          <Button
            size="small"
            title="编辑"
            type="text"
            @click="emit('edit', record)"
          >
            <template #icon>
              <Edit :size="14" />
            </template>
          </Button>
          <Button
            size="small"
            title="复制"
            type="text"
            @click="handleCopy(record)"
          >
            <template #icon>
              <Copy :size="14" />
            </template>
          </Button>
          <Popconfirm
            title="确定要删除这个链接吗？"
            @confirm="emit('delete', record.id)"
          >
            <Button danger size="small" title="删除" type="text">
              <template #icon>
                <Trash2 :size="14" />
              </template>
            </Button>
          </Popconfirm>
        </div>
      </template>
    </template>
  </Table>
</template>