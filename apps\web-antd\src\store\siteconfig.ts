// base
import { ref } from 'vue';

import { defineStore } from 'pinia';

import { requestClient } from '#/api/request';

// types
import type {
  SiteConfig,
  SiteConfigFormData,
  SiteConfigListParams,
  StanderResult,
  LinkGroupItem,
} from '#/types';

// API响应类型
interface BatchDeleteResponse {
  deleted_count: number;
}

// API函数
function _createSiteConfig(params: SiteConfigFormData) {
  return requestClient.post<StanderResult<SiteConfig>>(
    'siteconfig/create',
    params,
  );
}

function _getSiteConfigList(params: SiteConfigListParams = {}) {
  return requestClient.get<
    { total_records: number } & StanderResult<SiteConfig[]>
  >('siteconfig/list', { params });
}

function _getSiteConfigById(id: number) {
  return requestClient.get<StanderResult<SiteConfig>>(`siteconfig/${id}`);
}

function _updateSiteConfig(id: number, params: Partial<SiteConfigFormData>) {
  return requestClient.put<StanderResult<SiteConfig>>(
    `siteconfig/${id}`,
    params,
  );
}

function _deleteSiteConfig(id: number) {
  return requestClient.delete<StanderResult<null>>(`siteconfig/${id}`);
}

function _batchDeleteSiteConfig(ids: number[]) {
  return requestClient.post<StanderResult<BatchDeleteResponse>>(
    'siteconfig/batch-delete',
    ids,
  );
}

function _toggleSiteConfigStatus(id: number) {
  return requestClient.post<StanderResult<SiteConfig>>(
    `siteconfig/toggle-status/${id}`,
  );
}

// store
export const useSiteConfigStore = defineStore('site-config-store', () => {
  const loading = ref<boolean>(false);
  const siteConfigs = ref<SiteConfig[]>([]);
  const totalRecords = ref<number>(0);

  // 创建网站配置
  async function addSiteConfig(params: SiteConfigFormData) {
    try {
      loading.value = true;
      const res = await _createSiteConfig(params);
      if (res.success) {
        // 创建成功后刷新列表
        await getSiteConfigList();
        return res.data;
      }
      throw new Error(res.message || '创建失败');
    } finally {
      loading.value = false;
    }
  }

  // 获取网站配置列表
  async function getSiteConfigList(params: SiteConfigListParams = {}) {
    try {
      loading.value = true;
      const res = await _getSiteConfigList(params);
      if (res.success) {
        siteConfigs.value = res.data;
        totalRecords.value = res.total_records || 0;
        return res.data;
      }
      throw new Error(res.message || '获取列表失败');
    } finally {
      loading.value = false;
    }
  }

  // 获取单个网站配置
  async function getSiteConfigById(id: number) {
    const res = await _getSiteConfigById(id);
    if (res.success) {
      return res.data;
    }
    throw new Error(res.message || '获取详情失败');
  }

  // 更新网站配置
  async function updateSiteConfig(params: Partial<SiteConfig>) {
    try {
      loading.value = true;
      if (!params.id) {
        throw new Error('更新失败：缺少ID');
      }
      
      const id = params.id;
      // 移除id，避免传递给后端
      const { id: _, ...updateData } = params;
      
      const res = await _updateSiteConfig(id, updateData);
      if (res.success) {
        // 更新成功后刷新列表
        await getSiteConfigList();
        return res.data;
      }
      throw new Error(res.message || '更新失败');
    } finally {
      loading.value = false;
    }
  }

  // 删除网站配置
  async function deleteSiteConfig(id: number) {
    try {
      loading.value = true;
      const res = await _deleteSiteConfig(id);
      if (res.success) {
        // 删除成功后刷新列表
        await getSiteConfigList();
        return true;
      }
      throw new Error(res.message || '删除失败');
    } finally {
      loading.value = false;
    }
  }

  // 批量删除网站配置
  async function batchDeleteSiteConfig(ids: number[]) {
    try {
      loading.value = true;
      const res = await _batchDeleteSiteConfig(ids);
      if (res.success) {
        // 删除成功后刷新列表
        await getSiteConfigList();
        return res.data.deleted_count;
      }
      throw new Error(res.message || '批量删除失败');
    } finally {
      loading.value = false;
    }
  }

  // 切换启用状态
  async function toggleSiteConfigStatus(id: number) {
    const res = await _toggleSiteConfigStatus(id);
    if (res.success) {
      // 状态切换成功后刷新列表
      await getSiteConfigList();
      return res.data;
    }
    throw new Error(res.message || '状态切换失败');
  }

  function $reset() {
    siteConfigs.value = [];
    totalRecords.value = 0;
    loading.value = false;
  }

  return {
    $reset,
    addSiteConfig,
    batchDeleteSiteConfig,
    deleteSiteConfig,
    getSiteConfigById,
    getSiteConfigList,
    loading,
    siteConfigs,
    toggleSiteConfigStatus,
    totalRecords,
    updateSiteConfig,
  };
});


