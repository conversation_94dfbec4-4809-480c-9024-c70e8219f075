<script setup lang="ts">
import { <PERSON><PERSON>, <PERSON> } from 'ant-design-vue';
import { Plus, RefreshCw } from 'lucide-vue-next';

defineOptions({
  name: 'HeaderSettingsTabs',
});

const props = defineProps<{
  activeTab: string;
  loading: boolean;
}>();

const emit = defineEmits<{
  tabChange: [tabKey: string];
  refresh: [];
  add: [];
}>();

// tab配置
const tabs = [
  {
    description: '管理网站头部的导航链接',
    key: 'header-links',
    label: '头部链接',
  },
  {
    description: '配置网站的基本信息和设置',
    key: 'site-config',
    label: '网站配置',
  },
  {
    description: '配置网站的功能开关和参数',
    key: 'feature-config',
    label: '功能配置',
  },
];

function handleTabChange(tabKey: string) {
  emit('tabChange', tabKey);
}

function handleAdd() {
  emit('add');
}

function handleRefresh() {
  emit('refresh');
}
</script>

<template>
  <div class="mb-6 flex items-center justify-between border-b">
    <div class="flex">
      <button
        v-for="tab in tabs"
        :key="tab.key"
        :class="[
          activeTab === tab.key
            ? 'border-blue-500 text-blue-600'
            : 'border-transparent text-gray-500 hover:text-gray-700',
        ]"
        class="relative -mb-px border-b-2 px-6 py-3 text-sm font-medium transition-colors"
        @click="handleTabChange(tab.key)"
      >
        {{ tab.label }}
      </button>
    </div>
    <!-- 操作按钮 -->
    <div class="flex items-center gap-3">
      <Button
        :loading="loading"
        title="刷新数据"
        @click="handleRefresh"
      >
        <template #icon>
          <RefreshCw :size="16" />
        </template>
      </Button>
      <Button
        v-if="activeTab === 'header-links'"
        title="添加新链接"
        type="primary"
        @click="handleAdd"
      >
        <template #icon>
          <Plus :size="16" />
        </template>
      </Button>
    </div>
  </div>
</template>

