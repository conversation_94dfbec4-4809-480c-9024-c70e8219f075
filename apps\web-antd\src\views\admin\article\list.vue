<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import { Card, Button, Modal, message } from 'ant-design-vue';
import { Plus, RefreshCw } from 'lucide-vue-next';
import { useRouter } from 'vue-router';

// 导入组件
import ArticleFilter from '#/components/article/article-filter.vue';
import ArticleTable from '#/components/article/article-table.vue';

// 导入stores
import { useArticleStore } from '#/store/article/article';

// 导入类型
import type { Article, ArticleListParams } from '#/types';

defineOptions({
  name: 'ArticleList',
});

const router = useRouter();

// 使用store
const articleStore = useArticleStore();

// 搜索参数
const searchParams = reactive<ArticleListParams>({
  page: 1,
  page_size: 10,
  title: '',
  author_name: '',
  tag_id: undefined,
  channel: '',
  status: '',
  publish_start: undefined,
  publish_end: undefined,
});

// 加载文章列表
async function loadArticleList(params: ArticleListParams = searchParams) {
  try {
    await articleStore.getArticleList(params);
  } catch (error) {
    message.error('加载文章列表失败');
    console.error('加载失败:', error);
  }
}

// 处理搜索
function handleSearch(params: ArticleListParams) {
  Object.assign(searchParams, params);
  loadArticleList(searchParams);
}

// 处理重置
function handleReset() {
  Object.assign(searchParams, {
    page: 1,
    page_size: 10,
    title: '',
    author_name: '',
    tag_id: undefined,
    channel: '',
    status: '',
    publish_start: undefined,
    publish_end: undefined,
  });
  loadArticleList(searchParams);
}

// 处理分页变化
function handlePageChange(page: number, pageSize: number) {
  searchParams.page = page;
  searchParams.page_size = pageSize;
  loadArticleList(searchParams);
}

// 处理新增文章
function handleAdd() {
  router.push('/article-manage/publish');
}

// 处理编辑文章
function handleEdit(record: Article) {
  router.push(`/article-manage/edit/${record.id}`);
}

// 处理删除文章
function handleDelete(record: Article) {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除文章"${record.title}"吗？此操作不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    okType: 'danger',
    onOk: async () => {
      try {
        await articleStore.deleteArticle(record.id!);
        message.success('文章删除成功');
        // 重新加载列表
        await loadArticleList(searchParams);
      } catch (error) {
        message.error('删除失败，请重试');
        console.error('删除失败:', error);
      }
    },
  });
}

// 处理查看评论
function handleComment(record: Article) {
  // TODO: 实现评论管理功能
  message.info('评论管理功能开发中...');
}

// 处理可见性切换
async function handleVisibilityChange(record: Article, visible: boolean) {
  try {
    await articleStore.updateArticleVisibility(record.id!, visible);
    message.success(`文章${visible ? '显示' : '隐藏'}成功`);
  } catch (error) {
    message.error('更新失败，请重试');
    console.error('更新失败:', error);
  }
}

// 处理阅读数修改
async function handleViewCountChange(record: Article, count: number) {
  try {
    await articleStore.updateArticleViewCount(record.id!, count);
    message.success('阅读数更新成功');
  } catch (error) {
    message.error('更新失败，请重试');
    console.error('更新失败:', error);
  }
}

// 刷新数据
function handleRefresh() {
  loadArticleList(searchParams);
}

// 组件挂载时加载数据
onMounted(() => {
  loadArticleList();
});
</script>

<template>
  <div class="p-6">
    <Card>
      <!-- 页面标题 -->
      <div class="mb-6 flex items-center justify-between">
        <div>
          <h2 class="text-xl font-semibold text-gray-900">文章列表</h2>
          <p class="mt-1 text-sm text-gray-500">查看和管理已发布的文章</p>
        </div>
        <div class="flex gap-3">
          <Button @click="handleRefresh">
            <template #icon>
              <RefreshCw :size="16" />
            </template>
          </Button>
          <Button type="primary" @click="handleAdd">
            <template #icon>
              <Plus :size="16" />
            </template>
          </Button>
        </div>
      </div>

      <!-- 筛选组件 -->
      <ArticleFilter
        :loading="articleStore.loading"
        @search="handleSearch"
        @reset="handleReset"
      />

      <!-- 数据表格 -->
      <ArticleTable
        :articles="articleStore.articles"
        :loading="articleStore.loading"
        :total-records="articleStore.totalRecords"
        :current-page="searchParams.page || 1"
        :page-size="searchParams.page_size || 10"
        @edit="handleEdit"
        @delete="handleDelete"
        @comment="handleComment"
        @page-change="handlePageChange"
        @visibility-change="handleVisibilityChange"
        @view-count-change="handleViewCountChange"
      />
    </Card>
  </div>
</template>
