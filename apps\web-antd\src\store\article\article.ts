// 文章管理 store
import { ref } from 'vue';
import { defineStore } from 'pinia';
import { requestClient } from '#/api/request';

// types
import type {
  Article,
  ArticleFormData,
  ArticleListParams,
  StanderResult,
} from '#/types';

// API响应类型
interface BatchDeleteResponse {
  deleted_count: number;
}

// API函数
function _createArticle(params: ArticleFormData) {
  return requestClient.post<StanderResult<Article>>(
    'article/create',
    params,
  );
}

function _getArticleList(params: ArticleListParams = {}) {
  // 过滤掉undefined和空字符串的参数
  const cleanParams = Object.fromEntries(
    Object.entries(params).filter(([_, value]) => value !== undefined && value !== '')
  );

  return requestClient.get<
    { total_records: number } & StanderResult<Article[]>
  >('article/list', { params: cleanParams });
}

function _getArticleDetail(id: number) {
  return requestClient.get<StanderResult<Article>>(
    `article/${id}`,
  );
}

function _updateArticle(id: number, params: Partial<ArticleFormData>) {
  return requestClient.put<StanderResult<Article>>(
    `article/${id}`,
    params,
  );
}

function _deleteArticle(id: number) {
  return requestClient.delete<StanderResult<null>>(
    `article/${id}`,
  );
}

function _batchDeleteArticle(ids: number[]) {
  return requestClient.delete<StanderResult<BatchDeleteResponse>>(
    'article/batch-delete',
    { data: { ids } },
  );
}

function _updateArticleStatus(id: number, status: string) {
  return requestClient.put<StanderResult<Article>>(
    `article/${id}/status?status=${status}`,
  );
}

function _likeArticle(id: number) {
  return requestClient.post<StanderResult<{ like_count: number }>>(
    `article/${id}/like`,
  );
}

function _favoriteArticle(id: number) {
  return requestClient.post<StanderResult<{ favorite_count: number }>>(
    `article/${id}/favorite`,
  );
}

function _updateArticleVisibility(id: number, is_visible: boolean) {
  return requestClient.put<StanderResult<Article>>(
    `article/${id}`,
    { is_visible },
  );
}

function _updateArticleViewCount(id: number, view_count: number) {
  return requestClient.put<StanderResult<Article>>(
    `article/${id}`,
    { view_count },
  );
}

// store
export const useArticleStore = defineStore('article-store', () => {
  const loading = ref<boolean>(false);
  const articles = ref<Article[]>([]);
  const totalRecords = ref<number>(0);

  // 创建文章
  async function addArticle(params: ArticleFormData) {
    try {
      loading.value = true;
      const res = await _createArticle(params);
      if (res.success) {
        // 创建成功后刷新列表
        await getArticleList();
        return res.data;
      }
      throw new Error(res.message || '创建失败');
    } finally {
      loading.value = false;
    }
  }

  // 获取文章列表
  async function getArticleList(params: ArticleListParams = {}) {
    try {
      loading.value = true;
      const res = await _getArticleList(params);
      if (res.success) {
        articles.value = res.data || [];
        totalRecords.value = res.total_records || 0;
        return res.data;
      }
      throw new Error(res.message || '获取列表失败');
    } finally {
      loading.value = false;
    }
  }

  // 获取文章详情
  async function getArticleDetail(id: number) {
    try {
      loading.value = true;
      const res = await _getArticleDetail(id);
      if (res.success) {
        return res.data;
      }
      throw new Error(res.message || '获取详情失败');
    } finally {
      loading.value = false;
    }
  }

  // 更新文章
  async function updateArticle(id: number, params: Partial<ArticleFormData>) {
    try {
      loading.value = true;
      if (!id) {
        throw new Error('更新失败：缺少ID');
      }

      const res = await _updateArticle(id, params);
      if (res.success) {
        // 更新成功后刷新列表
        await getArticleList();
        return res.data;
      }
      throw new Error(res.message || '更新失败');
    } finally {
      loading.value = false;
    }
  }

  // 删除文章
  async function deleteArticle(id: number) {
    try {
      loading.value = true;
      const res = await _deleteArticle(id);
      if (res.success) {
        // 删除成功后刷新列表
        await getArticleList();
        return true;
      }
      throw new Error(res.message || '删除失败');
    } finally {
      loading.value = false;
    }
  }

  // 批量删除文章
  async function batchDeleteArticle(ids: number[]) {
    try {
      loading.value = true;
      const res = await _batchDeleteArticle(ids);
      if (res.success) {
        // 删除成功后刷新列表
        await getArticleList();
        return res.data;
      }
      throw new Error(res.message || '批量删除失败');
    } finally {
      loading.value = false;
    }
  }

  // 更新文章状态
  async function updateArticleStatus(id: number, status: string) {
    try {
      loading.value = true;
      const res = await _updateArticleStatus(id, status);
      if (res.success) {
        // 更新成功后刷新列表
        await getArticleList();
        return res.data;
      }
      throw new Error(res.message || '状态更新失败');
    } finally {
      loading.value = false;
    }
  }

  // 文章点赞
  async function likeArticle(id: number) {
    try {
      const res = await _likeArticle(id);
      if (res.success) {
        return res.data;
      }
      throw new Error(res.message || '点赞失败');
    } catch (error) {
      throw error;
    }
  }

  // 文章收藏
  async function favoriteArticle(id: number) {
    try {
      const res = await _favoriteArticle(id);
      if (res.success) {
        // 更新列表中对应文章的收藏数
        const article = articles.value.find(item => item.id === id);
        if (article) {
          article.favorite_count = res.data.favorite_count;
        }
        return res.data;
      }
      throw new Error(res.message || '收藏失败');
    } catch (error) {
      throw error;
    }
  }

  // 更新文章可见性
  async function updateArticleVisibility(id: number, is_visible: boolean) {
    try {
      loading.value = true;
      const res = await _updateArticleVisibility(id, is_visible);
      if (res.success) {
        // 更新列表中对应文章的可见性
        const article = articles.value.find(item => item.id === id);
        if (article) {
          article.is_visible = is_visible;
        }
        return res.data;
      }
      throw new Error(res.message || '可见性更新失败');
    } finally {
      loading.value = false;
    }
  }

  // 更新文章阅读数
  async function updateArticleViewCount(id: number, view_count: number) {
    try {
      loading.value = true;
      const res = await _updateArticleViewCount(id, view_count);
      if (res.success) {
        // 更新列表中对应文章的阅读数
        const article = articles.value.find(item => item.id === id);
        if (article) {
          article.view_count = view_count;
        }
        return res.data;
      }
      throw new Error(res.message || '阅读数更新失败');
    } finally {
      loading.value = false;
    }
  }

  return {
    loading,
    articles,
    totalRecords,
    addArticle,
    getArticleList,
    getArticleDetail,
    updateArticle,
    deleteArticle,
    batchDeleteArticle,
    updateArticleStatus,
    likeArticle,
    favoriteArticle,
    updateArticleVisibility,
    updateArticleViewCount,
  };
});
