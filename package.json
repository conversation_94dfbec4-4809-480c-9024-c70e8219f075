{"name": "vben-admin-pro", "version": "5.3.0-beta.2", "private": true, "keywords": ["monorepo", "turbo", "vben", "vben admin", "vben pro", "vue", "vue admin", "vue vben admin", "vue vben admin pro", "vue3"], "homepage": "https://github.com/vbenjs/vue-vben-admin", "bugs": "https://github.com/vbenjs/vue-vben-admin/issues", "repository": "vbenjs/vue-vben-admin.git", "license": "MIT", "author": {"name": "vben", "email": "<EMAIL>", "url": "https://github.com/anncwb"}, "type": "module", "scripts": {"bootstrap": "pnpm install", "build": "cross-env NODE_OPTIONS=--max-old-space-size=8192 turbo build", "build:analyze": "turbo build:analyze", "build:docker": "./build-local-docker-image.sh", "build:antd": "pnpm run build --filter=@vben/web-antd", "build:docs": "pnpm run build --filter=@vben/docs", "build:ele": "pnpm run build --filter=@vben/web-ele", "build:naive": "pnpm run build --filter=@vben/web-naive", "build:play": "pnpm run build --filter=@vben/playground", "changeset": "pnpm exec changeset", "check": "pnpm run check:circular && pnpm run check:dep && pnpm run check:type && pnpm check:cspell", "check:circular": "vsh check-circular", "check:cspell": "cspell lint **/*.ts **/README.md .changeset/*.md --no-progress", "check:dep": "vsh check-dep", "check:type": "turbo run typecheck", "clean": "vsh clean", "commit": "czg", "dev": "turbo-run dev", "dev:antd": "pnpm -F @vben/web-antd run dev", "dev:docs": "pnpm -F @vben/docs run dev", "dev:ele": "pnpm -F @vben/web-ele run dev", "dev:naive": "pnpm -F @vben/web-naive run dev", "dev:play": "pnpm -F @vben/playground run dev", "dev:test": "pnpm -F @vben/web-antd run dev:test", "format": "vsh lint --format", "lint": "vsh lint", "postinstall": "turbo run stub", "preinstall": "npx only-allow pnpm", "prepare": "is-ci || husky", "preview": "turbo-run preview", "publint": "vsh publint", "reinstall": "pnpm clean --del-lock && pnpm bootstrap", "test:unit": "vitest", "update:deps": "pnpm update --latest --recursive", "version": "pnpm exec changeset version && pnpm install --no-frozen-lockfile"}, "dependencies": {"@fullcalendar/core": "^6.1.6", "@fullcalendar/interaction": "^6.1.6", "@fullcalendar/resource-timegrid": "^6.1.6", "@fullcalendar/resource-timeline": "^6.1.6", "@fullcalendar/vue3": "^6.1.6", "@popperjs/core": "^2.11.8", "@schedule-x/calendar": "^2.3.1", "@schedule-x/theme-default": "^2.3.1", "@schedule-x/vue": "^2.3.1", "eventsource": "^3.0.5", "tesseract.js": "^5.1.1", "v-calendar": "^3.1.2", "vue-cal": "^4.10.0"}, "devDependencies": {"@changesets/changelog-github": "^0.5.0", "@changesets/cli": "^2.27.8", "@types/jsdom": "^21.1.7", "@types/node": "^22.5.5", "@vben/commitlint-config": "workspace:*", "@vben/eslint-config": "workspace:*", "@vben/prettier-config": "workspace:*", "@vben/stylelint-config": "workspace:*", "@vben/tailwind-config": "workspace:*", "@vben/tsconfig": "workspace:*", "@vben/turbo-run": "workspace:*", "@vben/vite-config": "workspace:*", "@vben/vsh": "workspace:*", "@vitejs/plugin-vue": "^5.1.4", "@vitejs/plugin-vue-jsx": "^4.0.1", "@vue/test-utils": "^2.4.6", "autoprefixer": "^10.4.20", "cross-env": "^7.0.3", "cspell": "^8.14.4", "exceljs": "^4.4.0", "husky": "^9.1.6", "is-ci": "^3.0.1", "jsdom": "^25.0.0", "jszip": "^3.10.1", "lint-staged": "^15.2.10", "rimraf": "^6.0.1", "tailwindcss": "^3.4.12", "turbo": "^2.1.2", "typescript": "^5.6.2", "unbuild": "^2.0.0", "vite": "^5.4.6", "vitest": "^2.1.1", "vue": "^3.5.6", "vue-tsc": "^2.1.6"}, "engines": {"node": ">=20", "pnpm": ">=9"}, "packageManager": "pnpm@9.10.0", "pnpm": {"peerDependencyRules": {"allowedVersions": {"eslint": "*"}}, "overrides": {"@ctrl/tinycolor": "4.1.0", "clsx": "2.1.1", "pinia": "2.2.2", "vue": "3.5.6"}, "neverBuiltDependencies": ["canvas", "node-gyp", "playwright"]}}