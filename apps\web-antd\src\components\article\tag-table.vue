<script setup lang="ts">
import { Button, Popconfirm, Table, Tag } from 'ant-design-vue';
import { Edit, Trash2 } from 'lucide-vue-next';
import type { ArticleTag } from '#/types';

defineOptions({
  name: 'TagTable',
});

const props = defineProps<{
  tags: ArticleTag[];
  loading: boolean;
  totalRecords: number;
  currentPage: number;
  pageSize: number;
}>();

const emit = defineEmits<{
  edit: [record: ArticleTag];
  delete: [id: number];
  pageChange: [page: number, pageSize: number];
}>();

// 表格列配置
const columns = [
  {
    dataIndex: 'name',
    key: 'name',
    title: '标签名称',
    width: 150,
  },
  {
    dataIndex: 'first_letter',
    key: 'first_letter',
    title: '首字母',
    width: 80,
  },
  {
    dataIndex: 'category',
    key: 'category',
    title: '所属分类',
    width: 200,
  },
  {
    dataIndex: 'article_count',
    key: 'article_count',
    title: '文章数量',
    width: 100,
  },
  {
    dataIndex: 'article_read_count',
    key: 'article_read_count',
    title: '阅读总数',
    width: 120,
  },
  {
    dataIndex: 'create_time',
    key: 'create_time',
    title: '创建时间',
    width: 180,
  },
  {
    align: 'center' as const,
    key: 'action',
    title: '操作',
    width: 120,
    fixed: 'right' as const,
  },
];

// 获取首字母颜色
function getFirstLetterColor(letter: string) {
  const colors = ['blue', 'green', 'orange', 'red', 'purple', 'cyan', 'magenta'];
  const charCode = letter.charCodeAt(0);
  return colors[charCode % colors.length];
}

// 格式化时间
function formatTime(time?: string) {
  if (!time) return '-';
  return new Date(time).toLocaleString('zh-CN');
}

// 格式化数字
function formatNumber(num?: number) {
  if (num === undefined || num === null) return 0;
  return num.toLocaleString();
}

// 处理分页变化
function handlePageChange(page: number, pageSize: number) {
  emit('pageChange', page, pageSize);
}
</script>

<template>
  <Table
    :columns="columns"
    :data-source="tags"
    :loading="loading"
    :pagination="{
      current: currentPage,
      pageSize: pageSize,
      total: totalRecords,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total: number, range: [number, number]) =>
        `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
      pageSizeOptions: ['10', '20', '50', '100'],
    }"
    row-key="id"
    scroll="{ x: 1000 }"
    @change="(pagination: any) => handlePageChange(pagination.current, pagination.pageSize)"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.key === 'name'">
        <Tag color="blue">{{ record.name }}</Tag>
      </template>
      <template v-else-if="column.key === 'first_letter'">
        <Tag :color="getFirstLetterColor(record.first_letter || '')">
          {{ record.first_letter || '-' }}
        </Tag>
      </template>
      <template v-else-if="column.key === 'category'">
        <span v-if="record.category">
          {{ record.category.name }}
        </span>
        <span v-else class="text-gray-400">-</span>
      </template>
      <template v-else-if="column.key === 'article_count'">
        <span class="font-medium text-blue-600">
          {{ formatNumber(record.article_count) }}
        </span>
      </template>
      <template v-else-if="column.key === 'article_read_count'">
        <span class="font-medium text-green-600">
          {{ formatNumber(record.article_read_count) }}
        </span>
      </template>
      <template v-else-if="column.key === 'create_time'">
        {{ formatTime(record.create_time) }}
      </template>
      <template v-else-if="column.key === 'action'">
        <div class="flex items-center justify-center gap-1">
          <Button
            size="small"
            title="编辑"
            type="text"
            @click="() => emit('edit', record)"
          >
            <template #icon>
              <Edit :size="14" />
            </template>
          </Button>
          <Popconfirm
            title="确定要删除这个标签吗？"
            ok-text="确定"
            cancel-text="取消"
            @confirm="() => emit('delete', record.id!)"
          >
            <Button
              danger
              size="small"
              title="删除"
              type="text"
            >
              <template #icon>
                <Trash2 :size="14" />
              </template>
            </Button>
          </Popconfirm>
        </div>
      </template>
    </template>
  </Table>
</template>
