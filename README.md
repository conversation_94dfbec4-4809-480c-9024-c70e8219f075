# 目录说明

apps/web-antd 是小时播仓库，外面是属于“大仓”，大仓将跟随vben持续升级，原则上尽量不改大仓代码

之后内容针对 apps/web-antd 仓库说明

# 设计理念

整体MVC结构

M使用Pinia(https://pinia.vuejs.org/core-concepts/) 定义一个响应式数据中心单例。数据的增删改查等各种操作均在该单例中。可以达到各页面数据通用的作用

V使用vue3标准 component+page 方式，通用组件抽象成component便于各页面流转，component使用涉及的数据传输 https://vuejs.org/guide/components/provide-inject ， 灵活Component提供插槽 https://vuejs.org/guide/components/slots.html

C使用vue3标准 TS 方式。以页面为单位，页面中，如果要定义model，尽量只是该页面简单的使用，负责数据如从网络获取的、筛选相关的，都要使用Store。

# 小时播设计理念

在V1版本中，使用角色作为划分依据，后续带来大量组件的冗余问题。V2版本中，我们业务比较成熟，使用业务作为划分依据。如 商品 ，出现在不同角色中时，其展现形式、内容均有大量相同，也有部分不同。我们抽象商品组件，在store中根据数据权限(角色权限)的不同做差异不分。

# 权限

目前只有角色权限，但系统包含了数据权限概念，v2初期，我们使用角色权限做隔离即可

# 无限滚动列表 + Card 替换 table

分页需要做分页管理，遇到数据增删时，分页信息还需要和服务端同步，整体比较繁琐。并且分页的使用体验不佳，再遇到特大量(万)数据时，分页有优势，我们目前业务数据量都不会特大。

## 无限滚动逻辑

通过q_id q_size q_order来定义数据获取，q_id为当前队尾的对象id，q_size是一页获取量，q_order是id的升降序规则。每次获取数据后，会把数据队尾的id赋值给q_id用于标记队尾。

每次滚动到底部时，触发新一批数据的获取

## Card应用

table布局数据时，由于每行数据不同，表宽无法确定，整体展示不及预期。在无限滚动容器中，一般使用card，card可以更好的布局数据，并且通过大小、UI设计，更清楚展示内容
