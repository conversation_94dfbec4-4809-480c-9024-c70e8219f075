# 微信公众号文章爬取API文档

## 概述

本文档描述了微信公众号文章爬取功能所需的后端API接口。前端已经实现了完整的爬取功能，包括URL验证、错误处理和备用方案。

## API接口

### 爬取微信文章

**接口地址**: `POST /wechat/crawl-article`

**请求参数**:
```json
{
  "url": "https://mp.weixin.qq.com/s/xxxxxxxxxx"
}
```

**响应格式**:
```json
{
  "success": true,
  "data": {
    "title": "文章标题",
    "content": "<p>文章HTML内容</p>",
    "author": "公众号名称",
    "publish_time": "2024-01-01T00:00:00Z",
    "cover_image": "https://example.com/cover.jpg",
    "summary": "文章摘要"
  },
  "message": "爬取成功"
}
```

**错误响应**:
```json
{
  "success": false,
  "data": null,
  "message": "爬取失败：无效的微信文章链接"
}
```

## 实现建议

### Python实现示例

```python
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, HttpUrl
import requests
from bs4 import BeautifulSoup
import re
from datetime import datetime

router = APIRouter(prefix="/wechat", tags=["微信爬取"])

class CrawlRequest(BaseModel):
    url: str

class WechatArticleInfo(BaseModel):
    title: str
    content: str
    author: str
    publish_time: str
    cover_image: str = None
    summary: str = None

class CrawlResponse(BaseModel):
    success: bool
    data: WechatArticleInfo = None
    message: str

@router.post("/crawl-article", response_model=CrawlResponse)
async def crawl_wechat_article(request: CrawlRequest):
    """爬取微信公众号文章"""
    try:
        # 验证URL格式
        if not is_valid_wechat_url(request.url):
            return CrawlResponse(
                success=False,
                message="无效的微信文章链接"
            )
        
        # 发送请求获取页面内容
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        response = requests.get(request.url, headers=headers, timeout=10)
        response.raise_for_status()
        
        # 解析HTML内容
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 提取文章信息
        article_data = extract_article_info(soup)
        
        return CrawlResponse(
            success=True,
            data=article_data,
            message="爬取成功"
        )
        
    except requests.RequestException as e:
        return CrawlResponse(
            success=False,
            message=f"网络请求失败: {str(e)}"
        )
    except Exception as e:
        return CrawlResponse(
            success=False,
            message=f"爬取失败: {str(e)}"
        )

def is_valid_wechat_url(url: str) -> bool:
    """验证微信文章URL"""
    patterns = [
        r'^https?://mp\.weixin\.qq\.com/s/.+',
        r'^https?://mp\.weixin\.qq\.com/s\?.+'
    ]
    return any(re.match(pattern, url) for pattern in patterns)

def extract_article_info(soup: BeautifulSoup) -> WechatArticleInfo:
    """从HTML中提取文章信息"""
    # 提取标题
    title_elem = soup.find('h1', {'id': 'activity-name'})
    title = title_elem.get_text().strip() if title_elem else "未知标题"
    
    # 提取作者
    author_elem = soup.find('a', {'id': 'js_name'})
    author = author_elem.get_text().strip() if author_elem else "未知作者"
    
    # 提取发布时间
    time_elem = soup.find('em', {'id': 'publish_time'})
    publish_time = datetime.now().isoformat()
    if time_elem:
        # 解析时间格式
        time_text = time_elem.get_text().strip()
        # 这里需要根据实际格式解析时间
    
    # 提取文章内容
    content_elem = soup.find('div', {'id': 'js_content'})
    content = ""
    if content_elem:
        # 清理内容
        content = clean_html_content(str(content_elem))
    
    # 提取封面图片
    cover_image = None
    img_elem = soup.find('img', {'data-src': True})
    if img_elem:
        cover_image = img_elem.get('data-src')
    
    # 生成摘要
    summary = extract_summary(content, 200)
    
    return WechatArticleInfo(
        title=title,
        content=content,
        author=author,
        publish_time=publish_time,
        cover_image=cover_image,
        summary=summary
    )

def clean_html_content(html: str) -> str:
    """清理HTML内容"""
    # 移除script和style标签
    soup = BeautifulSoup(html, 'html.parser')
    
    for script in soup(["script", "style"]):
        script.decompose()
    
    # 移除一些属性
    for tag in soup.find_all():
        # 保留基本属性
        allowed_attrs = ['src', 'href', 'alt', 'title']
        attrs = dict(tag.attrs)
        for attr in attrs:
            if attr not in allowed_attrs:
                del tag.attrs[attr]
    
    return str(soup)

def extract_summary(content: str, max_length: int = 200) -> str:
    """提取文章摘要"""
    # 移除HTML标签
    soup = BeautifulSoup(content, 'html.parser')
    text = soup.get_text()
    
    # 清理空白字符
    text = re.sub(r'\s+', ' ', text).strip()
    
    # 截取指定长度
    if len(text) <= max_length:
        return text
    
    return text[:max_length] + '...'
```

### Node.js实现示例

```javascript
const express = require('express');
const axios = require('axios');
const cheerio = require('cheerio');

const router = express.Router();

router.post('/wechat/crawl-article', async (req, res) => {
  try {
    const { url } = req.body;
    
    // 验证URL
    if (!isValidWechatUrl(url)) {
      return res.json({
        success: false,
        message: '无效的微信文章链接'
      });
    }
    
    // 获取页面内容
    const response = await axios.get(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      },
      timeout: 10000
    });
    
    // 解析HTML
    const $ = cheerio.load(response.data);
    
    // 提取文章信息
    const articleData = {
      title: $('#activity-name').text().trim() || '未知标题',
      author: $('#js_name').text().trim() || '未知作者',
      content: cleanHtmlContent($('#js_content').html() || ''),
      publish_time: new Date().toISOString(),
      cover_image: $('img[data-src]').first().attr('data-src') || null,
      summary: null
    };
    
    // 生成摘要
    articleData.summary = extractSummary(articleData.content, 200);
    
    res.json({
      success: true,
      data: articleData,
      message: '爬取成功'
    });
    
  } catch (error) {
    res.json({
      success: false,
      message: `爬取失败: ${error.message}`
    });
  }
});

function isValidWechatUrl(url) {
  const patterns = [
    /^https?:\/\/mp\.weixin\.qq\.com\/s\/.+/,
    /^https?:\/\/mp\.weixin\.qq\.com\/s\?.+/
  ];
  return patterns.some(pattern => pattern.test(url));
}

function cleanHtmlContent(html) {
  const $ = cheerio.load(html);
  
  // 移除script和style
  $('script, style').remove();
  
  // 移除一些属性
  $('*').each(function() {
    const allowedAttrs = ['src', 'href', 'alt', 'title'];
    const attrs = Object.keys(this.attribs);
    attrs.forEach(attr => {
      if (!allowedAttrs.includes(attr)) {
        $(this).removeAttr(attr);
      }
    });
  });
  
  return $.html();
}

function extractSummary(content, maxLength = 200) {
  const $ = cheerio.load(content);
  const text = $.text().replace(/\s+/g, ' ').trim();
  
  if (text.length <= maxLength) {
    return text;
  }
  
  return text.substring(0, maxLength) + '...';
}

module.exports = router;
```

## 注意事项

1. **法律合规**: 确保爬取行为符合相关法律法规
2. **请求频率**: 控制请求频率，避免对微信服务器造成压力
3. **错误处理**: 完善的错误处理和用户友好的错误提示
4. **内容清理**: 清理HTML内容，移除不必要的样式和脚本
5. **缓存机制**: 可以考虑添加缓存机制，避免重复爬取相同文章

## 前端集成

前端已经完全实现了爬取功能的UI和逻辑：

- ✅ URL验证和格式检查
- ✅ 用户友好的操作界面
- ✅ 完善的错误处理和提示
- ✅ 自动填充到富文本编辑器
- ✅ 备用方案（当后端接口不可用时）
- ✅ 与现有文章发布功能完美集成

只需要实现上述后端接口，即可完整使用微信文章爬取功能。
