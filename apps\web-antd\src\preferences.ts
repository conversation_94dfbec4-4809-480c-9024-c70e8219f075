import { defineOverridesPreferences } from '@vben/preferences';

/**
 * @description 项目配置文件
 * 只需要覆盖项目中的一部分配置，不需要的配置不用覆盖，会自动使用默认配置
 */
export const overridesPreferences = defineOverridesPreferences({
  // overrides
  app: {
    authPageLayout: 'panel-center',
    enableCheckUpdates: false,
    enableRefreshToken: false,
    locale: 'zh-CN',
    name: import.meta.env.VITE_APP_TITLE || 'DNY123',
  },
  breadcrumb: {
    showHome: true,
  },
  copyright: {
    companyName: 'DNY123',
    companySiteLink: '',
  },
  footer: {
    enable: false,
  },
  sidebar: {
    width: 170,
  },
  theme: {
    builtinType: 'deep-green',
    colorPrimary: 'hsl(240 5.9% 10%)',
    mode: 'light',
    radius: '0.75',
    semiDarkSidebar: false,
  },
});
