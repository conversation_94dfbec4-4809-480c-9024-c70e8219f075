{"name": "@vben-core/shared", "version": "5.3.0-beta.2", "homepage": "https://github.com/vbenjs/vue-vben-admin", "bugs": "https://github.com/vbenjs/vue-vben-admin/issues", "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "packages/@vben-core/base/shared"}, "license": "MIT", "type": "module", "scripts": {"build": "pnpm unbuild"}, "files": ["dist"], "sideEffects": false, "exports": {"./constants": {"types": "./src/constants/index.ts", "development": "./src/constants/index.ts", "default": "./dist/constants/index.mjs"}, "./utils": {"types": "./src/utils/index.ts", "development": "./src/utils/index.ts", "default": "./dist/utils/index.mjs"}, "./color": {"types": "./src/color/index.ts", "development": "./src/color/index.ts", "default": "./dist/color/index.mjs"}, "./cache": {"types": "./src/cache/index.ts", "development": "./src/cache/index.ts", "default": "./dist/cache/index.mjs"}, "./store": {"types": "./src/store.ts", "development": "./src/store.ts", "default": "./dist/store.mjs"}}, "publishConfig": {"exports": {"./constants": {"types": "./dist/constants/index.d.ts", "default": "./dist/constants/index.mjs"}, "./utils": {"types": "./dist/utils/index.d.ts", "default": "./dist/utils/index.mjs"}, "./color": {"types": "./dist/color/index.d.ts", "default": "./dist/color/index.mjs"}, "./cache": {"types": "./dist/cache/index.d.ts", "default": "./dist/cache/index.mjs"}, "./store": {"types": "./dist/store/index.d.ts", "default": "./dist/store.mjs"}}}, "dependencies": {"@ctrl/tinycolor": "^4.1.0", "@tanstack/vue-store": "^0.5.5", "@vue/reactivity": "^3.5.6", "@vue/shared": "^3.5.6", "clsx": "^2.1.1", "defu": "^6.1.4", "lodash.clonedeep": "^4.5.0", "nprogress": "^0.2.0", "tailwind-merge": "^2.5.2", "theme-colors": "^0.1.0"}, "devDependencies": {"@types/lodash.clonedeep": "^4.5.9", "@types/nprogress": "^0.2.3"}}