{"name": "@vben/commitlint-config", "version": "5.3.0-beta.2", "private": true, "homepage": "https://github.com/vbenjs/vue-vben-admin", "bugs": "https://github.com/vbenjs/vue-vben-admin/issues", "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "internal/lint-configs/commitlint-config"}, "license": "MIT", "type": "module", "files": ["dist"], "main": "./index.mjs", "module": "./index.mjs", "exports": {".": {"import": "./index.mjs", "default": "./index.mjs"}}, "dependencies": {"@commitlint/cli": "^19.5.0", "@commitlint/config-conventional": "^19.5.0", "@vben/node-utils": "workspace:*", "commitlint-plugin-function-rules": "^4.0.0", "cz-git": "^1.9.4", "czg": "^1.9.4"}}