export interface Auth {
  id: number;
  create_time: string; // DateTime in ISO format
  update_time: string; // DateTime in ISO format
  name: string;
  code: string;
  role_id: number;
}

export interface Role {
  id: number;
  create_time: string; // DateTime in ISO format
  update_time: string; // DateTime in ISO format
  name: string;
  auths: Auth[];
}

export interface User {
  id: number;
  create_time: string; // DateTime in ISO format
  update_time: string; // DateTime in ISO format
  account: string;
  hashed_password: string;
  user_type: number; // 0普通用户 1机构主体 2顾客主体 8管理员
  role: Role;
  role_id?: number;
  home?: string;
  /**
   * 用户姓名
   */
  name?: string;
  /**
   * 用户头像
   */
  avatar?: string;
  /**
   * 用户手机号
   */
  mobile?: string;
  /**
   * 用户邮箱
   */
  email?: string;
}

export interface LoginParams {
  username: string;
  password: string;
}

export interface RegisterParams {
  account: string;
  password: string;
  user_type: number; // 0普通 1机构 2客户 8管理员
  name?: string; // 机构名称
  shipping_address?: string; // 机构收货地址
  code?: string; // 客户号
}

export interface UserResetpassword {
  id: number;
  password: string; // base64 encoded
  password_confirm: string;
}

export interface UserUpdate {
  password?: string; // Optional, max length 50
  role_id?: number;
  user_type?: number;
  username?: string; // Optional, max length 50
  mobile?: string; // Optional, max length 15
  email?: string; // Optional, max length 100
}

export interface ResetPasswordParams {
  id: number;
  password: string; // base64 encoded
}
