import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      authority: ['admin'],
      hideChildrenInMenu: false,
      icon: 'lucide:file-text',
      order: 3,
      title: '文章管理',
    },
    name: 'ArticleManage',
    path: '/article-manage',
    children: [
      {
        component: () => import('#/views/admin/article/publish.vue'),
        meta: {
          affixTab: false,
          authority: ['admin'],
          icon: 'lucide:edit',
          title: '文章发布',
        },
        name: 'ArticlePublish',
        path: '/article-manage/publish',
      },
      {
        component: () => import('#/views/admin/article/list.vue'),
        meta: {
          affixTab: false,
          authority: ['admin'],
          icon: 'lucide:list',
          title: '文章列表',
        },
        name: 'ArticleList',
        path: '/article-manage/list',
      },
      {
        component: () => import('#/views/admin/article/edit.vue'),
        meta: {
          affixTab: false,
          authority: ['admin'],
          hideInMenu: true,
          title: '编辑文章',
        },
        name: 'ArticleEdit',
        path: '/article-manage/edit/:id',
      },
      {
        component: () => import('#/views/admin/article/category.vue'),
        meta: {
          affixTab: false,
          authority: ['admin'],
          icon: 'lucide:folder',
          title: '标签分类',
        },
        name: 'ArticleCategory',
        path: '/article-manage/category',
      },
      {
        component: () => import('#/views/admin/article/tag.vue'),
        meta: {
          affixTab: false,
          authority: ['admin'],
          icon: 'lucide:tag',
          title: '标签',
        },
        name: 'ArticleTag',
        path: '/article-manage/tag',
      },
      {
        component: () => import('#/views/admin/article/author.vue'),
        meta: {
          affixTab: false,
          authority: ['admin'],
          icon: 'lucide:user',
          title: '作者',
        },
        name: 'ArticleAuthor',
        path: '/article-manage/author',
      },
    ],
  },
];

export default routes;
