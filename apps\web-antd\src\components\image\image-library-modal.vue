<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { 
  Modal, 
  Input, 
  Select, 
  Button, 
  Upload, 
  Row, 
  Col, 
  Card, 
  Pagination,
  Empty,
  Spin,
  message,
  Checkbox,
  Popconfirm,
  Form,
  Tag
} from 'ant-design-vue';
import {
  Search,
  Upload as UploadIcon,
  Trash2,
  Plus,
  RotateCcw,
  Edit
} from 'lucide-vue-next';

// 导入store
import { useImageStore } from '#/store/image/image';

// 导入类型和常量
import type {
  ImageInfo,
  ImageListParams
} from '#/types/image';


// 导入组件
import TagManageModal from './tag-manage-modal.vue';
import ImageUploadModal from './image-upload-modal.vue';
import ImageEditModal from './image-edit-modal.vue';

defineOptions({
  name: 'ImageLibraryModal',
});

const props = defineProps<{
  visible: boolean;
  multiple?: boolean; // 是否支持多选
}>();

const emit = defineEmits<{
  'update:visible': [visible: boolean];
  select: [image: ImageInfo];
  selectMultiple: [images: ImageInfo[]];
}>();

const imageStore = useImageStore();

// 组件状态
const loading = ref(false);
const selectedImages = ref<number[]>([]);
const showTagModal = ref(false);
const showUploadModal = ref(false);
const showEditModal = ref(false);
const editingImage = ref<ImageInfo | null>(null);

// 搜索参数
const searchParams = ref<ImageListParams>({
  page: 1,
  page_size: 20,
  keyword: '',
  tag_ids: '',
  category: '',
});



// 标签选项
const tagOptions = computed(() =>
  imageStore.tags.map(tag => ({
    label: tag.name,
    value: tag.id,
  }))
);

// 标签选择的数组值（用于多选组件显示）
const selectedTagIds = computed({
  get: () => {
    if (!searchParams.value.tag_ids) return [];
    return searchParams.value.tag_ids.split(',').map(id => Number(id)).filter(id => !isNaN(id));
  },
  set: (value: number[]) => {
    searchParams.value.tag_ids = value.length > 0 ? value.join(',') : '';
  }
});



// 计算属性
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
});

const hasSelectedImages = computed(() => selectedImages.value.length > 0);

// 加载数据
async function loadData() {
  try {
    loading.value = true;
    await Promise.all([
      imageStore.getImageList(searchParams.value),
      imageStore.getTagList({ page_size: 100 }),
    ]);
  } catch (error) {
    message.error('加载数据失败');
  } finally {
    loading.value = false;
  }
}

// 搜索
async function handleSearch() {
  searchParams.value.page = 1;
  await loadData();
}

// 重置搜索
async function handleReset() {
  searchParams.value = {
    page: 1,
    page_size: 20,
    keyword: '',
    tag_ids: '',
    category: '',
  };
  selectedImages.value = [];
  await loadData();
}

// 分页变化
async function handlePageChange(page: number, pageSize: number) {
  searchParams.value.page = page;
  searchParams.value.page_size = pageSize;
  await loadData();
}

// 显示上传弹窗
function handleShowUpload() {
  showUploadModal.value = true;
}

// 显示标签管理弹窗
function handleShowTagManage() {
  showTagModal.value = true;
}

// 处理上传成功
function handleUploadSuccess() {
  loadData(); // 刷新图片列表
}

// 处理标签创建成功
function handleTagCreated() {
  // 标签列表会在标签管理弹窗中自动刷新
}

// 显示编辑弹窗
function handleShowEdit(image: ImageInfo) {
  editingImage.value = image;
  showEditModal.value = true;
}

// 处理编辑成功
function handleEditSuccess() {
  loadData(); // 刷新图片列表
}

// 选择图片
function handleSelectImage(image: ImageInfo) {
  if (props.multiple) {
    const index = selectedImages.value.indexOf(image.id);
    if (index > -1) {
      selectedImages.value.splice(index, 1);
    } else {
      selectedImages.value.push(image.id);
    }
  } else {
    emit('select', image);
  }
}

// 确认多选
function handleConfirmMultiple() {
  const selected = imageStore.images.filter(img => 
    selectedImages.value.includes(img.id)
  );
  emit('selectMultiple', selected);
}

// 删除图片
async function handleDeleteImage(id: number) {
  try {
    await imageStore.deleteImage(id);
    message.success('删除成功');
    await loadData();
  } catch (error) {
    message.error('删除失败');
  }
}



// 监听弹窗显示状态
watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      loadData();
      selectedImages.value = [];
    }
  }
);

// 组件挂载时加载数据
onMounted(() => {
  if (props.visible) {
    loadData();
  }
});
</script>

<template>
  <Modal
    v-model:open="modalVisible"
    title="图片库"
    width="1200px"
    :footer="multiple && hasSelectedImages ? undefined : null"
    @cancel="modalVisible = false"
  >
    <!-- 搜索区域 -->
    <div class="mb-4">
      <Row :gutter="16" align="bottom">
        <Col :span="8">
          <div class="search-item">
            <label class="search-label">标题：</label>
            <Input
              v-model:value="searchParams.keyword"
              placeholder="请输入图片名称"
              @press-enter="handleSearch"
            >
              <template #suffix>
                <Search :size="16" @click="handleSearch" />
              </template>
            </Input>
          </div>
        </Col>
        <Col :span="8">
          <div class="search-item">
            <label class="search-label">标签：</label>
            <Select
              v-model:value="selectedTagIds"
              mode="multiple"
              placeholder="请选择标签"
              allow-clear
              :options="tagOptions"
            />
          </div>
        </Col>
        <Col :span="8">
          <div class="flex gap-2">
            <Button type="primary" @click="handleSearch">
              <!-- <template #icon>
                <Search :size="16" />
              </template> -->
              搜索
            </Button>
            <Button @click="handleReset">
              <!-- <template #icon>
                <RotateCcw :size="16" />
              </template> -->
              重置
            </Button>
            <Button @click="handleShowUpload">
              <!-- <template #icon>
                <UploadIcon :size="16" />
              </template> -->

              上传图片
            </Button>
            <Button @click="handleShowTagManage">
              <!-- <template #icon>
                <Plus :size="16" />
              </template> -->
              标签管理
            </Button>
          </div>
        </Col>
      </Row>
      

    </div>

    <!-- 图片列表 -->
    <Spin :spinning="loading">
      <div v-if="imageStore.images.length > 0" class="image-grid">
        <div
          v-for="image in imageStore.images"
          :key="image.id"
          class="image-item"
          :class="{ 
            'selected': multiple && selectedImages.includes(image.id),
            'clickable': !multiple 
          }"
          @click="handleSelectImage(image)"
        >
          <!-- 多选模式的复选框 -->
          <Checkbox
            v-if="multiple"
            :checked="selectedImages.includes(image.id)"
            class="image-checkbox"
            @click.stop
            @change="handleSelectImage(image)"
          />
          
          <!-- 图片 -->
          <img :src="image.access_url" :alt="image.original_name" class="image" />
          
          <!-- 图片信息 -->
          <div class="image-info">
            <div class="image-name" :title="image.original_name">
              {{ image.original_name }}
            </div>
            <div class="image-meta">
              <span>{{ image.width }}×{{ image.height }}</span>
              <span>{{ (image.file_size / 1024).toFixed(1) }}KB</span>
            </div>
            <div v-if="image.tags && image.tags.length > 0" class="image-tags">
              <Tag v-for="tag in image.tags" :key="tag.id" size="small">
                {{ tag.name }}
              </Tag>
            </div>
          </div>
          
          <!-- 操作按钮 -->
          <div class="image-actions">
            <!-- 编辑按钮 -->
            <Button
              type="text"
              size="small"
              class="action-btn edit-btn"
              title="编辑图片信息"
              @click.stop="handleShowEdit(image)"
            >
              <Edit :size="16" />
            </Button>

            <!-- 删除按钮 -->
            <Popconfirm
              title="确定要删除这张图片吗？"
              @confirm="handleDeleteImage(image.id)"
              @click.stop
            >
              <Button
                type="text"
                danger
                size="small"
                class="action-btn delete-btn"
                @click.stop
              >
                <Trash2 :size="16" />
              </Button>
            </Popconfirm>
          </div>
        </div>
      </div>
      
      <!-- 空状态 -->
      <Empty v-else description="暂无图片" />
    </Spin>

    <!-- 分页 -->
    <div v-if="imageStore.totalRecords > 0" class="mt-4 text-center">
      <Pagination
        :current="searchParams.page"
        :page-size="searchParams.page_size"
        :total="imageStore.totalRecords"
        :show-size-changer="true"
        :show-quick-jumper="true"
        :show-total="(total) => `共 ${total} 张图片`"
        @change="handlePageChange"
      />
    </div>

    <!-- 多选模式的底部操作 -->
    <template v-if="multiple && hasSelectedImages" #footer>
      <div class="flex justify-between items-center">
        <span>已选择 {{ selectedImages.length }} 张图片</span>
        <div>
          <Button @click="modalVisible = false">取消</Button>
          <Button type="primary" class="ml-2" @click="handleConfirmMultiple">
            确定选择
          </Button>
        </div>
      </div>
    </template>
  </Modal>

  <!-- 标签管理弹窗 -->
  <TagManageModal
    v-model:visible="showTagModal"
    @tag-created="handleTagCreated"
  />

  <!-- 图片上传弹窗 -->
  <ImageUploadModal
    v-model:visible="showUploadModal"
    @upload-success="handleUploadSuccess"
  />

  <!-- 图片编辑弹窗 -->
  <ImageEditModal
    v-model:visible="showEditModal"
    :image-info="editingImage"
    @edit-success="handleEditSuccess"
  />
</template>

<style scoped>
.search-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-label {
  font-weight: 500;
  color: #262626;
  white-space: nowrap;
  min-width: 40px;
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  max-height: 500px;
  overflow-y: auto;
}

.image-item {
  position: relative;
  border: 2px solid transparent;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.image-item.clickable:hover {
  border-color: #1890ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.image-item.selected {
  border-color: #1890ff;
  background-color: #e6f7ff;
}

.image-checkbox {
  position: absolute;
  top: 8px;
  left: 8px;
  z-index: 2;
}

.image {
  width: 100%;
  height: 120px;
  object-fit: cover;
  cursor: pointer;
}

.image-info {
  padding: 8px;
}

.image-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 4px;
}

.image-meta {
  font-size: 12px;
  color: #666;
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.image-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.image-actions {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
  z-index: 2;
}

.action-btn {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  backdrop-filter: blur(4px);
}

.edit-btn:hover {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}

.delete-btn:hover {
  background: rgba(255, 77, 79, 0.1);
  color: #ff4d4f;
}

.image-item:hover .image-actions {
  opacity: 1;
}
</style>
