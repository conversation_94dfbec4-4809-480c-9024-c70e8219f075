{"name": "@vben/eslint-config", "version": "5.0.0", "private": true, "homepage": "https://github.com/vbenjs/vue-vben-admin", "bugs": "https://github.com/vbenjs/vue-vben-admin/issues", "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "internal/lint-configs/eslint-config"}, "license": "MIT", "type": "module", "scripts": {"stub": "pnpm unbuild --stub"}, "files": ["dist"], "main": "./dist/index.mjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs"}}, "dependencies": {"eslint-config-turbo": "^2.1.2", "eslint-plugin-command": "^0.2.5", "eslint-plugin-import-x": "^4.2.1"}, "devDependencies": {"@eslint/js": "^9.10.0", "@types/eslint": "^9.6.1", "@typescript-eslint/eslint-plugin": "^8.6.0", "@typescript-eslint/parser": "^8.6.0", "eslint": "^9.10.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-jsdoc": "^50.2.3", "eslint-plugin-jsonc": "^2.16.0", "eslint-plugin-n": "^17.10.3", "eslint-plugin-no-only-tests": "^3.3.0", "eslint-plugin-perfectionist": "^3.6.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^55.0.0", "eslint-plugin-unused-imports": "^4.1.4", "eslint-plugin-vitest": "^0.5.4", "eslint-plugin-vue": "^9.28.0", "globals": "^15.9.0", "jsonc-eslint-parser": "^2.4.0", "vue-eslint-parser": "^9.4.3"}}