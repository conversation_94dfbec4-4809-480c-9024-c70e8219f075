# 图片管理API文档

## 概述

图片管理系统提供了完整的图片管理功能，包括图片的上传、管理、标签管理和分类管理。系统已将相关功能拆分为独立的API模块，便于维护和使用。

## API模块结构

### 1. 图片管理API (`/image`)
负责图片的基础CRUD操作：
- 图片上传
- 图片列表查询
- 图片详情查看
- 图片信息更新
- 图片删除（单个/批量）

### 2. 图片标签管理API (`/image-tag`)
负责图片标签的管理：
- 标签创建、查看、更新、删除
- 标签与图片的关联管理
- 标签使用统计
- 查看标签下的图片列表

### 3. 图片分类管理API (`/image-category`)
负责图片分类的管理：
- 分类列表查看（支持统计信息）
- 分类下图片列表查询
- 分类统计信息
- 分类重命名
- 分类删除（支持清空或删除图片）

## 快速开始

### 启动独立API服务

```bash
# 启动独立的图片管理API服务
python image_api_server.py
```

服务启动后可访问：
- API文档：http://localhost:8001/docs
- ReDoc文档：http://localhost:8001/redoc
- 健康检查：http://localhost:8001/health

### 生成API文档

```bash
# 生成独立的API文档
python generate_api_docs.py
```

将生成：
- `docs/image_management_api.json` - OpenAPI JSON格式文档
- `docs/image_management_api.md` - Markdown格式文档

## API使用示例

### 1. 图片上传

```bash
curl -X POST "http://localhost:9000/image/upload" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@image.jpg" \
  -F "description=测试图片" \
  -F "category=风景" \
  -F "is_public=true" \
  -F "tag_ids=1,2,3"
```

### 2. 获取图片列表

```bash
curl -X GET "http://localhost:9000/image/list?page=1&page_size=20&category=风景" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 3. 创建标签

```bash
curl -X POST "http://localhost:9000/image-tag" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "自然风光",
    "description": "自然风景图片标签",
    "color": "#4CAF50"
  }'
```

### 4. 获取分类统计

```bash
curl -X GET "http://localhost:9000/image-category?include_stats=true" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 认证说明

所有API都需要用户认证，请在请求头中包含有效的JWT token：

```
Authorization: Bearer YOUR_JWT_TOKEN
```

## 响应格式

所有API响应都遵循统一的格式：

```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "total_records": 0
}
```

- `success`: 操作是否成功
- `data`: 返回的数据
- `message`: 操作结果消息
- `total_records`: 总记录数（分页查询时使用）

## 错误处理

当API调用失败时，会返回相应的错误信息：

```json
{
  "success": false,
  "message": "错误描述",
  "data": null
}
```

常见HTTP状态码：
- `200`: 成功
- `400`: 请求参数错误
- `401`: 未认证
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

## 文件结构

```
code/
├── app/routers/biz_routes/
│   ├── image_api_routers.py          # 图片管理API
│   ├── image_tag_api_routers.py      # 标签管理API
│   └── image_category_api_routers.py # 分类管理API
├── image_api_server.py               # 独立API服务器
├── generate_api_docs.py              # API文档生成器
└── docs/
    ├── IMAGE_API_README.md           # 本文档
    ├── image_management_api.json     # OpenAPI JSON文档
    └── image_management_api.md       # Markdown API文档
```

## 注意事项

1. **文件上传限制**：支持常见图片格式（jpg, jpeg, png, gif, bmp, webp）
2. **文件大小**：建议单个文件不超过10MB
3. **标签关联**：上传图片时可以通过`tag_ids`参数关联多个标签
4. **分类管理**：删除分类时可选择清空分类字段或删除所有图片
5. **权限控制**：所有操作都需要用户认证

## 更新日志

### v1.0.0 (2024-01-XX)
- ✅ 将图片标签和分类接口从主API文件中拆分
- ✅ 创建独立的标签管理API
- ✅ 创建独立的分类管理API  
- ✅ 提供独立的API服务器
- ✅ 生成完整的API文档
- ✅ 修复OSS上传配置问题
