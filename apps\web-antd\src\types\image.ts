// 图片管理相关类型定义

// 基础分页参数
export interface PaginationParams {
  page?: number;
  page_size?: number;
}

// 图片标签类型
export interface ImageTag {
  id: number;
  name: string;
  description?: string;
  creator_id?: number;
  create_time?: string;
  update_time?: string;
}

// 图片信息类型
export interface ImageInfo {
  id: number;
  original_name: string;
  new_name: string;
  access_url: string;
  file_size: number;
  file_type: string;
  width: number;
  height: number;
  description?: string;
  is_public: boolean;
  category?: string;
  uploader_id: number;
  tags?: ImageTag[];
  create_time: string;
  update_time: string;
}

// 图片上传参数
export interface ImageUploadParams {
  file: File;
  original_name?: string;
  description?: string;
  category?: string;
  is_public?: boolean;
  tag_ids?: string; // 逗号分隔的标签ID
}

// 图片更新参数
export interface ImageUpdateParams {
  original_name?: string;
  description?: string;
  tag_ids?: number[]; // 标签ID数组
}

// 图片列表查询参数
export interface ImageListParams extends PaginationParams {
  tag_ids?: string; // 逗号分隔的标签ID
  category?: string;
  keyword?: string; // 关键词搜索
  is_public?: boolean;
  uploader_id?: number;
}

// 标签创建参数
export interface TagCreateParams {
  name: string;
  description?: string;
}

// 标签更新参数
export interface TagUpdateParams {
  name?: string;
  description?: string;
}

// 标签列表查询参数
export interface TagListParams extends PaginationParams {
  keyword?: string;
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message: string;
  total_records?: number;
}

// 图片分类选项
export const IMAGE_CATEGORY_OPTIONS = [
  { label: '横幅广告', value: 'banner' },
  { label: '产品图片', value: 'product' },
  { label: '文章配图', value: 'article' },
  { label: '用户头像', value: 'avatar' },
  { label: '其他', value: 'other' },
];

// 支持的图片格式
export const SUPPORTED_IMAGE_TYPES = [
  'image/jpeg',
  'image/jpg', 
  'image/png',
  'image/gif',
  'image/webp',
];

// 图片大小限制（10MB）
export const MAX_IMAGE_SIZE = 10 * 1024 * 1024;
