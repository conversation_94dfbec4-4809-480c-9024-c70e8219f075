{"$schema": "https://raw.githubusercontent.com/streetsidesoftware/cspell/main/cspell.schema.json", "version": "0.2", "language": "en,en-US", "allowCompoundWords": true, "words": ["clsx", "esno", "demi", "unref", "taze", "acmr", "antd", "lucide", "brotli", "defu", "execa", "iconify", "intlify", "mkdist", "mockjs", "noopener", "<PERSON><PERSON><PERSON><PERSON>", "nprogress", "pinia", "publint", "qrcode", "shadcn", "sonner", "unplugin", "vben", "vbenjs", "vueuse", "yxxx", "nuxt", "lockb", "astro", "ui-kit", "styl", "vnode", "nocheck", "prefixs", "vitepress", "antdv", "ependencies", "vite", "echarts", "sortablejs", "etag", "<PERSON><PERSON>", "uicons", "iconoir"], "ignorePaths": ["**/node_modules/**", "**/dist/**", "**/*-dist/**", "**/icons/**", "pnpm-lock.yaml", "**/*.log", "**/*.test.ts", "**/*.spec.ts", "**/__tests__/**"]}