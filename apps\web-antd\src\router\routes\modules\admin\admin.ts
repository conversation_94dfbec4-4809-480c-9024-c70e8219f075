import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';
import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      authority: ['admin'],
      hideChildrenInMenu: true,
      icon: 'lucide:house',
      order: 1,
      title: $t('home'),
    },
    name: 'AdminHome',
    path: '/home',
    children: [
      {
        component: () => import('#/views/admin/home.vue'),
        meta: {
          affixTab: false,
          authority: ['admin'],
          icon: 'lucide:calendar',
          title: $t('home'),
        },
        name: 'AdminHomeIndex',
        path: '/home',
      },
    ],
  },
  // {
  //   component: BasicLayout,
  //   meta: {
  //     authority: ['admin'],
  //     hideChildrenInMenu: true,
  //     icon: 'lucide:search',
  //     order: 1,
  //     title: $t('brandquery'),
  //   },
  //   name: 'AdminBrand',
  //   path: '/brand',
  //   children: [
  //     {
  //       component: () => import('#/views/admin/brand.vue'),
  //       meta: {
  //         affixTab: false,
  //         authority: ['admin'],
  //         icon: 'lucide:search',
  //         title: $t('brandquery'),
  //       },
  //       name: 'AdminBrandIndex',
  //       path: '/brand',
  //     },
  //   ],
  // },
];

export default routes;
