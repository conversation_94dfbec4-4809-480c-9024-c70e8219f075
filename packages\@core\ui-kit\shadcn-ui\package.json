{"name": "@vben-core/shadcn-ui", "version": "5.3.0-beta.2", "homepage": "https://github.com/vbenjs/vue-vben-admin", "bugs": "https://github.com/vbenjs/vue-vben-admin/issues", "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "packages/@vben-core/uikit/shadcn-ui"}, "license": "MIT", "type": "module", "scripts": {"#build": "pnpm unbuild", "#prepublishOnly": "npm run build"}, "files": ["dist"], "sideEffects": ["**/*.css"], "#main": "./dist/index.mjs", "main": "./src/index.ts", "#module": "./dist/index.mjs", "module": "./src/index.ts", "exports": {".": {"types": "./src/index.ts", "development": "./src/index.ts", "//default": "./dist/index.mjs", "default": "./src/index.ts"}}, "publishConfig": {"exports": {".": {"default": "./src/index.ts"}}}, "dependencies": {"@radix-icons/vue": "^1.0.0", "@vben-core/composables": "workspace:*", "@vben-core/icons": "workspace:*", "@vben-core/shared": "workspace:*", "@vben-core/typings": "workspace:*", "@vueuse/core": "^11.1.0", "class-variance-authority": "^0.7.0", "lucide-vue-next": "^0.441.0", "radix-vue": "^1.9.5", "vee-validate": "^4.13.2", "vue": "^3.5.6"}}