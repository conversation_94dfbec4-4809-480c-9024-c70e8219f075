{"name": "@vben/common-ui", "version": "5.3.0-beta.2", "homepage": "https://github.com/vbenjs/vue-vben-admin", "bugs": "https://github.com/vbenjs/vue-vben-admin/issues", "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "packages/effects/common-ui"}, "license": "MIT", "type": "module", "sideEffects": ["**/*.css"], "exports": {".": {"types": "./src/index.ts", "default": "./src/index.ts"}}, "dependencies": {"@vben-core/form-ui": "workspace:*", "@vben-core/popup-ui": "workspace:*", "@vben-core/shadcn-ui": "workspace:*", "@vben/constants": "workspace:*", "@vben/icons": "workspace:*", "@vben/locales": "workspace:*", "@vben/types": "workspace:*", "@vueuse/integrations": "^11.1.0", "qrcode": "^1.5.4", "vue": "^3.5.6", "vue-router": "^4.4.5"}, "devDependencies": {"@types/qrcode": "^1.5.5"}}