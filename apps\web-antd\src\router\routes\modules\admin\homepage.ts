import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      authority: ['admin'],
      hideChildrenInMenu: false,
      icon: 'lucide:settings',
      order: 2,
      title: '首页管理',
    },
    name: 'HomepageManage',
    path: '/homepage-manage',
    children: [
      {
        component: () => import('#/views/admin/homepage/header-settings.vue'),
        meta: {
          affixTab: false,
          authority: ['admin'],
          icon: 'lucide:layout-panel-top',
          title: '头部设置',
        },
        name: 'HeaderSettings',
        path: '/homepage-manage/header-settings',
      },
    ],
  },
];

export default routes;
