{"name": "@vben/tailwind-config", "version": "5.3.0-beta.2", "private": true, "homepage": "https://github.com/vbenjs/vue-vben-admin", "bugs": "https://github.com/vbenjs/vue-vben-admin/issues", "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "internal/tailwind-config"}, "license": "MIT", "type": "module", "scripts": {"stub": "pnpm unbuild --stub"}, "files": ["dist"], "main": "./dist/index.mjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "typesVersions": {"*": {"*": ["./dist/*", "./*"]}}, "exports": {".": {"types": "./src/index.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./postcss": {"types": "./src/postcss.config.ts", "import": "./dist/postcss.config.mjs", "require": "./dist/postcss.config.cjs", "default": "./dist/postcss.config.mjs"}, "./*": "./*"}, "peerDependencies": {"tailwindcss": "^3.4.3"}, "dependencies": {"@iconify/json": "^2.2.250", "@iconify/tailwind": "^1.1.3", "@tailwindcss/nesting": "0.0.0-insiders.565cd3e", "@tailwindcss/typography": "^0.5.15", "autoprefixer": "^10.4.20", "cssnano": "^7.0.6", "postcss": "^8.4.47", "postcss-antd-fixes": "^0.2.0", "postcss-import": "^16.1.0", "postcss-preset-env": "^10.0.3", "tailwindcss": "^3.4.12", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/postcss-import": "^14.0.3", "@vben/node-utils": "workspace:*"}}