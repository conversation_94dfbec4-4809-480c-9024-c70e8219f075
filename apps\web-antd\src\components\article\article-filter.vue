<script setup lang="ts">
import { reactive, onMounted, computed } from 'vue';
import { 
  Form, 
  Input, 
  Select, 
  DatePicker, 
  Button, 
  Row, 
  Col,
  Space 
} from 'ant-design-vue';
import { Search, RotateCcw } from 'lucide-vue-next';
import dayjs from 'dayjs';

// 导入stores
import { useArticleAuthorStore } from '#/store/article/author';
import { useArticleTagStore } from '#/store/article/tag';

// 导入类型和常量
import type { ArticleListParams } from '#/types';
import { ARTICLE_STATUS_OPTIONS, ARTICLE_CHANNEL_OPTIONS } from '#/types';

defineOptions({
  name: 'ArticleFilter',
});

const props = defineProps<{
  loading?: boolean;
}>();

const emit = defineEmits<{
  search: [params: ArticleListParams];
  reset: [];
}>();

// 使用stores
const authorStore = useArticleAuthorStore();
const tagStore = useArticleTagStore();

// 筛选表单数据
const filterForm = reactive<ArticleListParams>({
  page: 1,
  page_size: 10,
  title: '',
  author_name: '',
  tag_id: undefined,
  channel: '',
  status: '',
  publish_start: undefined,
  publish_end: undefined,
});

// 日期范围值
const dateRange = reactive<{
  start?: dayjs.Dayjs;
  end?: dayjs.Dayjs;
}>({
  start: undefined,
  end: undefined,
});

// 作者选项
const authorOptions = computed(() => {
  return authorStore.authors.map(author => ({
    label: `${author.author_name} (${author.wechat_nickname})`,
    value: author.author_name,
  }));
});

// 标签选项
const tagOptions = computed(() => {
  return tagStore.tags.map(tag => ({
    label: tag.name,
    value: tag.id,
  }));
});

// 处理搜索
function handleSearch() {
  // 处理日期范围 - 转换为ISO格式
  if (dateRange.start) {
    filterForm.publish_start = dateRange.start.toISOString();
  } else {
    filterForm.publish_start = undefined;
  }

  if (dateRange.end) {
    filterForm.publish_end = dateRange.end.toISOString();
  } else {
    filterForm.publish_end = undefined;
  }

  // 重置页码
  filterForm.page = 1;

  // 创建干净的参数对象，移除undefined值
  const cleanParams = Object.fromEntries(
    Object.entries(filterForm).filter(([_, value]) => value !== undefined && value !== '')
  );



  emit('search', cleanParams as ArticleListParams);
}

// 处理重置
function handleReset() {
  // 重置表单数据
  Object.assign(filterForm, {
    page: 1,
    page_size: 10,
    title: '',
    author_name: '',
    tag_id: undefined,
    channel: '',
    status: '',
    publish_start: undefined,
    publish_end: undefined,
  });

  // 重置日期范围
  dateRange.start = undefined;
  dateRange.end = undefined;

  emit('reset');
}

// 处理开始日期变化
function handleStartDateChange(date: dayjs.Dayjs | null) {
  dateRange.start = date || undefined;
}

// 处理结束日期变化
function handleEndDateChange(date: dayjs.Dayjs | null) {
  dateRange.end = date || undefined;
}

// 加载数据
async function loadData() {
  try {
    await Promise.all([
      authorStore.getArticleAuthorList({ page_size: 100 }),
      tagStore.getArticleTagList({ page_size: 100 }),
    ]);
  } catch (error) {
    console.error('加载筛选数据失败:', error);
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadData();
});
</script>

<template>
  <div class="article-filter">
    <Form
      :model="filterForm"
      layout="vertical"
      class="mb-4"
    >
      <Row :gutter="16">
        <!-- 文章标题 -->
        <Col :span="6">
          <Form.Item label="文章标题" name="title">
            <Input
              v-model:value="filterForm.title"
              placeholder="请输入文章标题"
              allow-clear
            />
          </Form.Item>
        </Col>

        <!-- 作者 -->
        <Col :span="6">
          <Form.Item label="作者" name="author_name">
            <Select
              v-model:value="filterForm.author_name"
              placeholder="请选择作者"
              :options="authorOptions"
              show-search
              allow-clear
              :filter-option="(input: string, option: any) => 
                option.label.toLowerCase().includes(input.toLowerCase())
              "
            />
          </Form.Item>
        </Col>

        <!-- 标签 -->
        <Col :span="6">
          <Form.Item label="标签" name="tag_id">
            <Select
              v-model:value="filterForm.tag_id"
              placeholder="请选择标签"
              :options="tagOptions"
              show-search
              allow-clear
              :filter-option="(input: string, option: any) => 
                option.label.toLowerCase().includes(input.toLowerCase())
              "
            />
          </Form.Item>
        </Col>

        <!-- 发布频道 -->
        <Col :span="6">
          <Form.Item label="发布频道" name="channel">
            <Select
              v-model:value="filterForm.channel"
              placeholder="请选择发布频道"
              :options="ARTICLE_CHANNEL_OPTIONS"
              allow-clear
            />
          </Form.Item>
        </Col>
      </Row>

      <Row :gutter="16">
        <!-- 审核状态 -->
        <Col :span="6">
          <Form.Item label="审核状态" name="status">
            <Select
              v-model:value="filterForm.status"
              placeholder="请选择审核状态"
              :options="ARTICLE_STATUS_OPTIONS"
              allow-clear
            />
          </Form.Item>
        </Col>

        <!-- 发布时间开始 -->
        <Col :span="6">
          <Form.Item label="发布时间（开始）" name="publish_start">
            <DatePicker
              :value="dateRange.start"
              show-time
              format="YYYY-MM-DD HH:mm:ss"
              placeholder="请选择开始时间"
              class="w-full"
              @change="handleStartDateChange"
            />
          </Form.Item>
        </Col>

        <!-- 发布时间结束 -->
        <Col :span="6">
          <Form.Item label="发布时间（结束）" name="publish_end">
            <DatePicker
              :value="dateRange.end"
              show-time
              format="YYYY-MM-DD HH:mm:ss"
              placeholder="请选择结束时间"
              class="w-full"
              @change="handleEndDateChange"
            />
          </Form.Item>
        </Col>

        <!-- 操作按钮 -->
        <Col :span="6">
          <Form.Item label=" " name="actions">
            <Space>
              <Button
                type="primary"
                :loading="loading"
                @click="handleSearch"
              >
                搜索
              </Button>
              <Button @click="handleReset">
                重置
              </Button>
            </Space>
          </Form.Item>
        </Col>
      </Row>
    </Form>
  </div>
</template>

<style scoped>
.article-filter {
  background: #fafafa;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 16px;
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-form-item-label) {
  padding-bottom: 4px;
}

:deep(.ant-form-item-label > label) {
  font-weight: 500;
  color: #262626;
}
</style>
