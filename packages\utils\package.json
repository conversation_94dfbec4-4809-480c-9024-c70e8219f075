{"name": "@vben/utils", "version": "5.3.0-beta.2", "homepage": "https://github.com/vbenjs/vue-vben-admin", "bugs": "https://github.com/vbenjs/vue-vben-admin/issues", "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "packages/utils"}, "license": "MIT", "type": "module", "sideEffects": ["**/*.css"], "main": "./dist/index.mjs", "module": "./dist/index.mjs", "exports": {".": {"types": "./src/index.ts", "default": "./src/index.ts"}}, "dependencies": {"@vben-core/shared": "workspace:*", "@vben-core/typings": "workspace:*", "vue-router": "^4.4.5"}}