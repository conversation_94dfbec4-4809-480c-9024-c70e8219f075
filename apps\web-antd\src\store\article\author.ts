// 文章作者管理 store
import { ref } from 'vue';
import { defineStore } from 'pinia';
import { requestClient } from '#/api/request';

// types
import type {
  ArticleAuthor,
  ArticleAuthorFormData,
  ArticleAuthorListParams,
  StanderResult,
} from '#/types';

// API响应类型
interface BatchDeleteResponse {
  deleted_count: number;
}

// API函数
function _createArticleAuthor(params: ArticleAuthorFormData) {
  return requestClient.post<StanderResult<ArticleAuthor>>(
    'article-author/create',
    params,
  );
}

function _getArticleAuthorList(params: ArticleAuthorListParams = {}) {
  return requestClient.get<
    { total_records: number } & StanderResult<ArticleAuthor[]>
  >('article-author/list', { params });
}

function _getArticleAuthorDetail(id: number) {
  return requestClient.get<StanderResult<ArticleAuthor>>(
    `article-author/${id}`,
  );
}

function _updateArticleAuthor(id: number, params: Partial<ArticleAuthorFormData>) {
  return requestClient.put<StanderResult<ArticleAuthor>>(
    `article-author/${id}`,
    params,
  );
}

function _deleteArticleAuthor(id: number) {
  return requestClient.delete<StanderResult<null>>(
    `article-author/${id}`,
  );
}

function _batchDeleteArticleAuthor(ids: number[]) {
  return requestClient.delete<StanderResult<BatchDeleteResponse>>(
    'article-author/batch-delete',
    { data: { ids } },
  );
}

// store
export const useArticleAuthorStore = defineStore('article-author-store', () => {
  const loading = ref<boolean>(false);
  const authors = ref<ArticleAuthor[]>([]);
  const totalRecords = ref<number>(0);

  // 创建文章作者
  async function addArticleAuthor(params: ArticleAuthorFormData) {
    try {
      loading.value = true;
      const res = await _createArticleAuthor(params);
      if (res.success) {
        // 创建成功后刷新列表
        await getArticleAuthorList();
        return res.data;
      }
      throw new Error(res.message || '创建失败');
    } finally {
      loading.value = false;
    }
  }

  // 获取文章作者列表
  async function getArticleAuthorList(params: ArticleAuthorListParams = {}) {
    try {
      loading.value = true;
      const res = await _getArticleAuthorList(params);
      if (res.success) {
        authors.value = res.data || [];
        totalRecords.value = res.total_records || 0;
        return res.data;
      }
      throw new Error(res.message || '获取列表失败');
    } finally {
      loading.value = false;
    }
  }

  // 获取文章作者详情
  async function getArticleAuthorDetail(id: number) {
    try {
      loading.value = true;
      const res = await _getArticleAuthorDetail(id);
      if (res.success) {
        return res.data;
      }
      throw new Error(res.message || '获取详情失败');
    } finally {
      loading.value = false;
    }
  }

  // 更新文章作者
  async function updateArticleAuthor(params: Partial<ArticleAuthor>) {
    try {
      loading.value = true;
      if (!params.id) {
        throw new Error('更新失败：缺少ID');
      }
      
      const id = params.id;
      // 移除id，避免传递给后端
      const { id: _, ...updateData } = params;
      
      const res = await _updateArticleAuthor(id, updateData);
      if (res.success) {
        // 更新成功后刷新列表
        await getArticleAuthorList();
        return res.data;
      }
      throw new Error(res.message || '更新失败');
    } finally {
      loading.value = false;
    }
  }

  // 删除文章作者
  async function deleteArticleAuthor(id: number) {
    try {
      loading.value = true;
      const res = await _deleteArticleAuthor(id);
      if (res.success) {
        // 删除成功后刷新列表
        await getArticleAuthorList();
        return true;
      }
      throw new Error(res.message || '删除失败');
    } finally {
      loading.value = false;
    }
  }

  // 批量删除文章作者
  async function batchDeleteArticleAuthor(ids: number[]) {
    try {
      loading.value = true;
      const res = await _batchDeleteArticleAuthor(ids);
      if (res.success) {
        // 删除成功后刷新列表
        await getArticleAuthorList();
        return res.data;
      }
      throw new Error(res.message || '批量删除失败');
    } finally {
      loading.value = false;
    }
  }

  return {
    loading,
    authors,
    totalRecords,
    addArticleAuthor,
    getArticleAuthorList,
    getArticleAuthorDetail,
    updateArticleAuthor,
    deleteArticleAuthor,
    batchDeleteArticleAuthor,
  };
});
