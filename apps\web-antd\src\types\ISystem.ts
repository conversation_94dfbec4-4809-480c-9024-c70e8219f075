export interface Token {
  access_token: string;
  token_type: string;
}

export interface StanderResult<T> {
  success: boolean;
  data: T;
  message: string;
  token: Token;
  toast: string;
  total_records: number;
}

export interface BaseQuery {
  q_id: number; // -1 is all
  q_order: string; // 'asc desc'
  q_size: number;
}

export interface IdQuery {
  id: number;
}

export interface IdsQuery {
  ids: number[];
}

export interface PageQuery {
  page: number;
  page_size: number;
}

export interface Delete {
  id: number;
}

export interface Url {
  url: string;
}

export interface BrandQuery {
  name: string;
}

export interface ImageBase64DataRequest {
  hashsearch: string;
  image_url: string;
}

export interface BrandQueryResult {
  hash: string;
  response: string;
}

export interface WIPOObject {
  brandName: string[]; // 品牌名称
  applicationNumber: string; // 申请号
  kind: string[]; // 类型
  collection: string; // 来源
  office: string; // 申请地
  type: string; // 类型
  applicant: string[]; // 申请人
  markFeature: string; // 特征
  expiryDate?: string; // 有效期
  score: number; // 评分
  niceClass?: number[]; // 分类
  st13: string; // 来源
  registrationNumber?: string; // 注册号
  registrationDate?: string; // 注册日期
  logo?: string[];
  designation?: string[]; // 指定使用商品
  runid: string; // 起效日期
  applicantCountryCode?: string[];
  applicationDate: string; // 申请日期
  status: string; // 状态
  terminationDate?: string; // 终止日期
  imageData?: string; // 图片
  hashsearch?: string; // 哈希值
}

export interface WIPOResult {
  response: {
    docs: WIPOObject[];
    numFound: number;
    start: number;
  };
}
