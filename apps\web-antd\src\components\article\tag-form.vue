<script setup lang="ts">
import { computed, ref, watch, onMounted } from 'vue';
import { Modal, Form, Input, Select, message } from 'ant-design-vue';
import type { ArticleTag, ArticleCategory } from '#/types';
import { useArticleCategoryStore } from '#/store/article/category';

defineOptions({
  name: 'TagForm',
});

const props = defineProps<{
  visible: boolean;
  editingRecord?: ArticleTag | null;
}>();

const emit = defineEmits<{
  'update:visible': [visible: boolean];
  cancel: [];
  confirm: [formData: any];
}>();

// 使用分类store
const categoryStore = useArticleCategoryStore();

// 表单引用
const formRef = ref();

// 表单数据
const formData = ref({
  name: '',
  category_id: undefined as number | undefined,
});

// 分类选项
const categoryOptions = ref<Array<{ label: string; value: number }>>([]);

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入标签名称', trigger: 'blur' },
    { max: 20, message: '标签名称不能超过20个字符', trigger: 'blur' },
  ],
  category_id: [
    { required: true, message: '请选择关联分类', trigger: 'change' },
  ],
};

// 计算属性：弹窗标题
const modalTitle = computed(() => {
  return props.editingRecord?.id ? '编辑标签' : '新增标签';
});

// 监听编辑记录变化
watch(
  () => props.editingRecord,
  (newRecord) => {
    if (newRecord) {
      formData.value = {
        name: newRecord.name || '',
        category_id: newRecord.category_id,
      };
    } else {
      resetForm();
    }
  },
  { immediate: true }
);

// 监听弹窗显示状态
watch(
  () => props.visible,
  (visible) => {
    if (!visible) {
      resetForm();
    } else {
      // 弹窗打开时加载分类列表
      loadCategories();
    }
  }
);

// 重置表单
function resetForm() {
  formData.value = {
    name: '',
    category_id: undefined,
  };
  formRef.value?.resetFields();
}

// 加载分类列表
async function loadCategories() {
  try {
    await categoryStore.getArticleCategoryList({ page: 1, page_size: 100 });
    categoryOptions.value = categoryStore.categories.map(category => ({
      label: category.name,
      value: category.id!,
    }));
  } catch (error) {
    message.error('加载分类列表失败');
  }
}

// 确认提交
async function handleConfirm() {
  try {
    await formRef.value?.validate();

    const submitData: any = { ...formData.value };

    // 只有在编辑模式且有有效ID时才包含ID
    if (props.editingRecord?.id) {
      submitData.id = props.editingRecord.id;
    }

    emit('confirm', submitData);
  } catch (error) {
    console.error('表单验证失败:', error);
  }
}

// 取消
function handleCancel() {
  emit('update:visible', false);
  emit('cancel');
}

// 处理弹窗关闭
function handleModalCancel() {
  emit('update:visible', false);
  emit('cancel');
}

// 组件挂载时加载分类列表
onMounted(() => {
  loadCategories();
});
</script>

<template>
  <Modal
    :open="visible"
    :title="modalTitle"
    :width="500"
    :destroy-on-close="true"
    :mask-closable="false"
    @cancel="handleModalCancel"
    @ok="handleConfirm"
  >
    <Form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      layout="vertical"
      class="mt-4"
    >
      <Form.Item label="标签名称" name="name">
        <Input
          v-model:value="formData.name"
          placeholder="请输入标签名称（最多20个字符）"
          :maxlength="20"
          show-count
        />
      </Form.Item>
      
      <Form.Item label="关联分类" name="category_id">
        <Select 
          v-model:value="formData.category_id" 
          placeholder="请选择关联的标签分类"
          :options="categoryOptions"
          :loading="categoryStore.loading"
          show-search
          :filter-option="(input: string, option: any) => 
            option.label.toLowerCase().includes(input.toLowerCase())
          "
        />
      </Form.Item>
      
      <div class="text-sm text-gray-500">
        <p>• 首字母将根据标签名称自动生成</p>
        <p>• 文章数量和阅读总数将根据实际使用情况统计</p>
      </div>
    </Form>
  </Modal>
</template>
