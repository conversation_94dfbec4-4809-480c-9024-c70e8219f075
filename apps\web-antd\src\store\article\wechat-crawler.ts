// 微信公众号文章爬取工具类
import { defineStore } from 'pinia';
import { ref } from 'vue';
import { requestClient } from '#/api/request';

// 微信文章信息接口
export interface WechatArticleInfo {
  title: string;
  content: string;
  author: string;
  publish_time: string;
  cover_image?: string;
  summary?: string;
}

// 爬取结果接口
export interface CrawlResult {
  success: boolean;
  data?: WechatArticleInfo;
  message: string;
}

// API函数 - 爬取微信文章
function _crawlWechatArticle(url: string) {
  return requestClient.post<CrawlResult>(
    'wechat/crawl-article',
    { url }
  );
}

export const useWechatCrawlerStore = defineStore('wechatCrawler', () => {
  // 状态
  const loading = ref(false);
  const lastCrawledData = ref<WechatArticleInfo | null>(null);

  /**
   * 爬取微信公众号文章
   * @param url 微信文章链接
   * @returns 爬取结果
   */
  async function crawlArticle(url: string): Promise<WechatArticleInfo> {
    try {
      loading.value = true;
      
      // 验证URL格式
      if (!isValidWechatUrl(url)) {
        throw new Error('请输入有效的微信公众号文章链接');
      }

      // 调用后端爬取接口
      const response = await _crawlWechatArticle(url);
      
      if (response.success && response.data) {
        lastCrawledData.value = response.data;
        return response.data;
      } else {
        throw new Error(response.message || '文章爬取失败');
      }
    } catch (error: any) {
      // 如果后端接口不可用，使用前端解析（备用方案）
      if (error.response?.status === 404 || error.code === 'NETWORK_ERROR') {
        console.warn('后端爬取接口不可用，尝试前端解析');
        return await crawlArticleClientSide(url);
      }
      throw error;
    } finally {
      loading.value = false;
    }
  }

  /**
   * 验证微信文章URL格式
   * @param url 待验证的URL
   * @returns 是否为有效的微信文章链接
   */
  function isValidWechatUrl(url: string): boolean {
    const wechatUrlPatterns = [
      /^https?:\/\/mp\.weixin\.qq\.com\/s\/.+/,
      /^https?:\/\/mp\.weixin\.qq\.com\/s\?.+/,
    ];
    
    return wechatUrlPatterns.some(pattern => pattern.test(url));
  }

  /**
   * 前端解析微信文章（备用方案）
   * 注意：由于跨域限制，这个方法可能无法直接访问微信文章
   * 主要用于演示和备用
   */
  async function crawlArticleClientSide(url: string): Promise<WechatArticleInfo> {
    try {
      // 由于跨域限制，直接fetch微信文章会失败
      // 这里提供一个模拟的解析结果
      console.warn('前端直接爬取受跨域限制，返回模拟数据');
      
      // 从URL中提取一些基本信息
      const urlParams = new URLSearchParams(url.split('?')[1] || '');
      const title = decodeURIComponent(urlParams.get('title') || '微信文章标题');
      
      return {
        title: title || '微信公众号文章',
        content: '<p>由于跨域限制，无法直接获取文章内容。请使用后端爬取接口或手动复制内容。</p>',
        author: '微信公众号',
        publish_time: new Date().toISOString(),
        summary: '请手动添加文章摘要',
      };
    } catch (error) {
      throw new Error('文章解析失败，请检查链接是否正确');
    }
  }

  /**
   * 清理HTML内容，移除不必要的标签和样式
   * @param html 原始HTML内容
   * @returns 清理后的HTML内容
   */
  function cleanHtmlContent(html: string): string {
    // 移除script标签
    html = html.replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '');
    
    // 移除style标签
    html = html.replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '');
    
    // 移除内联样式中的一些属性
    html = html.replace(/style\s*=\s*"[^"]*"/gi, '');
    
    // 移除一些微信特有的属性
    html = html.replace(/data-[^=]*="[^"]*"/gi, '');
    
    // 保留基本的格式标签
    const allowedTags = [
      'p', 'br', 'strong', 'b', 'em', 'i', 'u', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
      'ul', 'ol', 'li', 'blockquote', 'img', 'a', 'span', 'div'
    ];
    
    // 这里可以添加更复杂的HTML清理逻辑
    return html;
  }

  /**
   * 提取文章摘要
   * @param content HTML内容
   * @param maxLength 最大长度
   * @returns 文章摘要
   */
  function extractSummary(content: string, maxLength: number = 200): string {
    // 移除HTML标签
    const textContent = content.replace(/<[^>]*>/g, '');
    
    // 移除多余的空白字符
    const cleanText = textContent.replace(/\s+/g, ' ').trim();
    
    // 截取指定长度
    if (cleanText.length <= maxLength) {
      return cleanText;
    }
    
    return cleanText.substring(0, maxLength) + '...';
  }

  /**
   * 获取最后爬取的数据
   */
  function getLastCrawledData(): WechatArticleInfo | null {
    return lastCrawledData.value;
  }

  /**
   * 清除缓存数据
   */
  function clearCache(): void {
    lastCrawledData.value = null;
  }

  return {
    // 状态
    loading,
    lastCrawledData,
    
    // 方法
    crawlArticle,
    isValidWechatUrl,
    crawlArticleClientSide,
    cleanHtmlContent,
    extractSummary,
    getLastCrawledData,
    clearCache,
  };
});
