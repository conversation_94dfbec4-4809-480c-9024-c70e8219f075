// 微信公众号文章爬取工具类
import { defineStore } from 'pinia';
import { ref } from 'vue';
import { requestClient } from '#/api/request';

// 微信文章信息接口
export interface WechatArticleInfo {
  title: string;
  content: string;
  author: string;
  publish_time: string;
  cover_image?: string;
  summary?: string;
  original_url?: string;
  crawl_time?: string;
}

// URL验证结果接口
export interface UrlValidationResult {
  url: string;
  is_valid: boolean;
  message: string;
}

// API响应接口
export interface ApiResponse<T = any> {
  success: boolean;
  data: T | null;
  message: string;
}



// API函数 - 验证微信文章URL
function _validateWechatUrl(url: string) {
  return requestClient.get<ApiResponse<UrlValidationResult>>(
    'article/validate-wechat-url',
    { params: { url } }
  );
}

// API函数 - 爬取微信文章
function _crawlWechatArticle(url: string) {
  return requestClient.post<ApiResponse<WechatArticleInfo>>(
    'article/crawl-wechat',
    { url }
  );
}

export const useWechatCrawlerStore = defineStore('wechatCrawler', () => {
  // 状态
  const loading = ref(false);
  const validating = ref(false);
  const lastCrawledData = ref<WechatArticleInfo | null>(null);

  /**
   * 验证微信文章URL
   * @param url 微信文章链接
   * @returns 验证结果
   */
  async function validateWechatUrl(url: string): Promise<boolean> {
    try {
      validating.value = true;

      // 先进行前端基础格式验证
      if (!isValidWechatUrl(url)) {
        return false;
      }

      // 调用后端接口进行深度验证
      const response = await _validateWechatUrl(url);

      if (response.success && response.data) {
        return response.data.is_valid;
      }

      return false;
    } catch (error) {
      console.error('URL验证失败:', error);
      return false;
    } finally {
      validating.value = false;
    }
  }

  /**
   * 爬取微信公众号文章
   * @param url 微信文章链接
   * @returns 爬取结果
   */
  async function crawlArticle(url: string): Promise<WechatArticleInfo> {
    try {
      loading.value = true;

      // 1. 首先验证URL
      const isValid = await validateWechatUrl(url);
      if (!isValid) {
        throw new Error('无效的微信公众号文章链接，请检查URL是否正确');
      }

      // 2. 调用后端爬取接口
      const response = await _crawlWechatArticle(url);

      if (response.success && response.data) {
        lastCrawledData.value = response.data;
        return response.data;
      } else {
        throw new Error(response.message || '文章爬取失败');
      }
    } catch (error: any) {
      // 如果是网络错误或后端不可用，提供友好提示
      if (error.response?.status === 404) {
        throw new Error('爬取服务暂时不可用，请稍后重试');
      } else if (error.response?.status === 400) {
        throw new Error('请求参数错误，请检查文章链接格式');
      } else if (error.response?.status === 500) {
        throw new Error('服务器内部错误，请联系管理员');
      }

      throw error;
    } finally {
      loading.value = false;
    }
  }

  /**
   * 验证微信文章URL格式（前端基础验证）
   * @param url 待验证的URL
   * @returns 是否为有效的微信文章链接格式
   */
  function isValidWechatUrl(url: string): boolean {
    if (!url || typeof url !== 'string') {
      return false;
    }

    const wechatUrlPatterns = [
      /^https?:\/\/mp\.weixin\.qq\.com\/s\/.+/,
      /^https?:\/\/mp\.weixin\.qq\.com\/s\?.+/,
    ];

    return wechatUrlPatterns.some(pattern => pattern.test(url.trim()));
  }











  /**
   * 清理HTML内容，移除不必要的标签和样式
   * @param html 原始HTML内容
   * @returns 清理后的HTML内容
   */
  function cleanHtmlContent(html: string): string {
    // 移除script标签
    html = html.replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '');

    // 移除style标签
    html = html.replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '');

    // 移除内联样式中的一些属性（保留基本样式）
    html = html.replace(/style\s*=\s*"[^"]*?(font-size|color|text-align)[^"]*"/gi, (match) => {
      // 保留基本样式属性
      const basicStyles = match.match(/(font-size|color|text-align):\s*[^;"]*/g);
      if (basicStyles) {
        return `style="${basicStyles.join('; ')}"`;
      }
      return '';
    });

    // 移除一些微信特有的属性
    html = html.replace(/data-[^=]*="[^"]*"/gi, '');

    // 移除一些无用的属性
    html = html.replace(/(class|id)\s*=\s*"[^"]*"/gi, '');

    // 处理图片链接
    html = html.replace(/data-src="([^"]*)"/gi, 'src="$1"');

    // 移除空的标签
    html = html.replace(/<(\w+)[^>]*>\s*<\/\1>/gi, '');

    return html;
  }

  /**
   * 提取文章摘要
   * @param content HTML内容
   * @param maxLength 最大长度
   * @returns 文章摘要
   */
  function extractSummary(content: string, maxLength: number = 200): string {
    // 移除HTML标签
    const textContent = content.replace(/<[^>]*>/g, '');
    
    // 移除多余的空白字符
    const cleanText = textContent.replace(/\s+/g, ' ').trim();
    
    // 截取指定长度
    if (cleanText.length <= maxLength) {
      return cleanText;
    }
    
    return cleanText.substring(0, maxLength) + '...';
  }

  /**
   * 获取最后爬取的数据
   */
  function getLastCrawledData(): WechatArticleInfo | null {
    return lastCrawledData.value;
  }

  /**
   * 清除缓存数据
   */
  function clearCache(): void {
    lastCrawledData.value = null;
  }

  /**
   * 解析用户粘贴的内容
   * @param pastedContent 用户粘贴的HTML或文本内容
   * @param title 文章标题（可选）
   * @returns 解析后的文章信息
   */
  function parseUserPastedContent(pastedContent: string, title?: string): WechatArticleInfo {
    try {
      let content = pastedContent;
      let extractedTitle = title || '';

      // 如果内容包含HTML标签，说明是富文本内容
      if (/<[^>]+>/.test(content)) {
        // 尝试从HTML中提取标题
        if (!extractedTitle) {
          const titleMatch = content.match(/<h[1-6][^>]*>(.*?)<\/h[1-6]>/i);
          if (titleMatch && titleMatch[1]) {
            extractedTitle = titleMatch[1].replace(/<[^>]+>/g, '').trim();
          }
        }

        // 清理HTML内容
        content = cleanHtmlContent(content);
      } else {
        // 纯文本内容，转换为HTML格式
        content = content
          .split('\n')
          .filter(line => line.trim())
          .map(line => `<p>${line.trim()}</p>`)
          .join('\n');
      }

      // 生成摘要
      const summary = extractSummary(content, 200);

      return {
        title: extractedTitle || '请输入文章标题',
        content,
        author: '微信公众号',
        publish_time: new Date().toISOString(),
        summary,
      };
    } catch (error) {
      console.error('解析粘贴内容失败:', error);
      throw new Error('内容解析失败');
    }
  }

  /**
   * 从剪贴板获取内容
   * @returns 剪贴板内容
   */
  async function getClipboardContent(): Promise<string> {
    try {
      if (navigator.clipboard && navigator.clipboard.readText) {
        return await navigator.clipboard.readText();
      } else {
        throw new Error('浏览器不支持剪贴板API');
      }
    } catch (error) {
      console.warn('获取剪贴板内容失败:', error);
      throw new Error('无法获取剪贴板内容，请手动粘贴');
    }
  }

  return {
    // 状态
    loading,
    validating,
    lastCrawledData,

    // 方法
    validateWechatUrl,
    crawlArticle,
    isValidWechatUrl,
    parseUserPastedContent,
    getClipboardContent,
    cleanHtmlContent,
    extractSummary,
    getLastCrawledData,
    clearCache,
  };
});
