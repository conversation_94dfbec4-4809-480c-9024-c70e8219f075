// 微信公众号文章爬取工具类
import { defineStore } from 'pinia';
import { ref } from 'vue';

// 微信文章信息接口
export interface WechatArticleInfo {
  title: string;
  content: string;
  author: string;
  publish_time: string;
  cover_image?: string;
  summary?: string;
}



export const useWechatCrawlerStore = defineStore('wechatCrawler', () => {
  // 状态
  const loading = ref(false);
  const lastCrawledData = ref<WechatArticleInfo | null>(null);

  /**
   * 爬取微信公众号文章
   * @param url 微信文章链接
   * @returns 爬取结果
   */
  async function crawlArticle(url: string): Promise<WechatArticleInfo> {
    try {
      loading.value = true;

      // 验证URL格式
      if (!isValidWechatUrl(url)) {
        throw new Error('请输入有效的微信公众号文章链接');
      }

      // 直接使用前端爬取方案
      return await crawlArticleClientSide(url);
    } finally {
      loading.value = false;
    }
  }

  /**
   * 验证微信文章URL格式
   * @param url 待验证的URL
   * @returns 是否为有效的微信文章链接
   */
  function isValidWechatUrl(url: string): boolean {
    const wechatUrlPatterns = [
      /^https?:\/\/mp\.weixin\.qq\.com\/s\/.+/,
      /^https?:\/\/mp\.weixin\.qq\.com\/s\?.+/,
    ];
    
    return wechatUrlPatterns.some(pattern => pattern.test(url));
  }

  /**
   * 前端解析微信文章
   * 使用多种方法尝试获取文章内容
   */
  async function crawlArticleClientSide(url: string): Promise<WechatArticleInfo> {
    try {
      // 方法1: 尝试使用代理服务
      const proxyResult = await tryProxyFetch(url);
      if (proxyResult) {
        return proxyResult;
      }

      // 方法2: 尝试使用iframe方式
      const iframeResult = await tryIframeFetch(url);
      if (iframeResult) {
        return iframeResult;
      }

      // 方法3: 使用用户手动输入方式
      return await tryManualInput(url);
    } catch (error) {
      throw new Error('文章解析失败，请检查链接是否正确');
    }
  }

  /**
   * 尝试使用代理服务获取内容
   */
  async function tryProxyFetch(url: string): Promise<WechatArticleInfo | null> {
    try {
      // 使用公共代理服务（注意：生产环境需要自己的代理服务）
      const proxyUrls = [
        `https://api.allorigins.win/get?url=${encodeURIComponent(url)}`,
        `https://cors-anywhere.herokuapp.com/${url}`,
        `https://thingproxy.freeboard.io/fetch/${url}`,
      ];

      for (const proxyUrl of proxyUrls) {
        try {
          const response = await fetch(proxyUrl, {
            method: 'GET',
            headers: {
              'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            },
          });

          if (response.ok) {
            const data = await response.json();
            const html = data.contents || data.response || data;

            if (typeof html === 'string' && html.includes('js_content')) {
              return parseWechatHtml(html);
            }
          }
        } catch (error) {
          console.warn(`代理服务 ${proxyUrl} 失败:`, error);
          continue;
        }
      }
    } catch (error) {
      console.warn('代理获取失败:', error);
    }
    return null;
  }

  /**
   * 尝试使用iframe方式获取内容
   */
  async function tryIframeFetch(url: string): Promise<WechatArticleInfo | null> {
    return new Promise((resolve) => {
      try {
        // 创建隐藏的iframe
        const iframe = document.createElement('iframe');
        iframe.style.display = 'none';
        iframe.style.width = '0';
        iframe.style.height = '0';
        iframe.src = url;

        let resolved = false;
        const timeout = setTimeout(() => {
          if (!resolved) {
            resolved = true;
            document.body.removeChild(iframe);
            resolve(null);
          }
        }, 5000);

        iframe.onload = () => {
          try {
            if (resolved) return;

            // 尝试访问iframe内容
            const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
            if (iframeDoc) {
              const html = iframeDoc.documentElement.outerHTML;
              const result = parseWechatHtml(html);

              resolved = true;
              clearTimeout(timeout);
              document.body.removeChild(iframe);
              resolve(result);
            } else {
              throw new Error('无法访问iframe内容');
            }
          } catch (error) {
            console.warn('iframe方式失败:', error);
            if (!resolved) {
              resolved = true;
              clearTimeout(timeout);
              document.body.removeChild(iframe);
              resolve(null);
            }
          }
        };

        iframe.onerror = () => {
          if (!resolved) {
            resolved = true;
            clearTimeout(timeout);
            document.body.removeChild(iframe);
            resolve(null);
          }
        };

        document.body.appendChild(iframe);
      } catch (error) {
        console.warn('创建iframe失败:', error);
        resolve(null);
      }
    });
  }

  /**
   * 用户手动输入方式
   */
  async function tryManualInput(url: string): Promise<WechatArticleInfo> {
    // 从URL中提取一些基本信息
    const urlParams = new URLSearchParams(url.split('?')[1] || '');
    const title = decodeURIComponent(urlParams.get('title') || '');

    return {
      title: title || '请手动输入文章标题',
      content: `
        <div style="padding: 20px; border: 2px dashed #ccc; border-radius: 8px; text-align: center; color: #666;">
          <h3>📋 手动导入提示</h3>
          <p>由于跨域限制，无法自动获取文章内容。</p>
          <p>请按以下步骤手动导入：</p>
          <ol style="text-align: left; display: inline-block;">
            <li>在新标签页中打开微信文章链接</li>
            <li>选中并复制文章内容（Ctrl+A, Ctrl+C）</li>
            <li>回到此页面，删除此提示内容</li>
            <li>粘贴文章内容到编辑器中（Ctrl+V）</li>
          </ol>
          <p><strong>文章链接：</strong><br><a href="${url}" target="_blank">${url}</a></p>
        </div>
      `,
      author: '微信公众号',
      publish_time: new Date().toISOString(),
      summary: '请手动添加文章摘要',
    };
  }

  /**
   * 解析微信文章HTML内容
   * @param html 微信文章HTML
   * @returns 解析后的文章信息
   */
  function parseWechatHtml(html: string): WechatArticleInfo {
    try {
      // 创建临时DOM元素来解析HTML
      const parser = new DOMParser();
      const doc = parser.parseFromString(html, 'text/html');

      // 提取标题
      let title = '';
      const titleSelectors = [
        '#activity-name',
        '.rich_media_title',
        'h1',
        '.title'
      ];

      for (const selector of titleSelectors) {
        const titleElement = doc.querySelector(selector);
        if (titleElement) {
          title = titleElement.textContent?.trim() || '';
          if (title) break;
        }
      }

      // 提取作者
      let author = '微信公众号';
      const authorSelectors = [
        '#js_name',
        '.rich_media_meta_nickname',
        '.profile_nickname'
      ];

      for (const selector of authorSelectors) {
        const authorElement = doc.querySelector(selector);
        if (authorElement) {
          author = authorElement.textContent?.trim() || author;
          if (author !== '微信公众号') break;
        }
      }

      // 提取文章内容
      let content = '';
      const contentSelectors = [
        '#js_content',
        '.rich_media_content',
        '.article-content'
      ];

      for (const selector of contentSelectors) {
        const contentElement = doc.querySelector(selector);
        if (contentElement) {
          content = contentElement.innerHTML || '';
          if (content) break;
        }
      }

      // 清理内容
      if (content) {
        content = cleanHtmlContent(content);
      }

      // 提取封面图片
      let coverImage = '';
      const imgSelectors = [
        '.rich_media_thumb img',
        '#js_content img:first-child',
        'img[data-src]'
      ];

      for (const selector of imgSelectors) {
        const imgElement = doc.querySelector(selector);
        if (imgElement) {
          coverImage = imgElement.getAttribute('data-src') ||
                      imgElement.getAttribute('src') || '';
          if (coverImage) break;
        }
      }

      // 提取发布时间
      let publishTime = new Date().toISOString();
      const timeSelectors = [
        '#publish_time',
        '.rich_media_meta_text',
        '.time'
      ];

      for (const selector of timeSelectors) {
        const timeElement = doc.querySelector(selector);
        if (timeElement) {
          const timeText = timeElement.textContent?.trim();
          if (timeText) {
            // 尝试解析时间格式
            const parsedTime = parsePublishTime(timeText);
            if (parsedTime) {
              publishTime = parsedTime;
              break;
            }
          }
        }
      }

      // 生成摘要
      const summary = extractSummary(content, 200);

      return {
        title: title || '微信公众号文章',
        content: content || '<p>未能获取到文章内容，请手动复制粘贴。</p>',
        author,
        publish_time: publishTime,
        cover_image: coverImage || undefined,
        summary,
      };
    } catch (error) {
      console.error('解析HTML失败:', error);
      throw new Error('文章内容解析失败');
    }
  }

  /**
   * 解析发布时间
   * @param timeText 时间文本
   * @returns ISO时间字符串
   */
  function parsePublishTime(timeText: string): string | null {
    try {
      // 常见的时间格式
      const timePatterns = [
        /(\d{4})-(\d{2})-(\d{2})\s+(\d{2}):(\d{2})/,  // 2024-01-01 12:00
        /(\d{4})年(\d{1,2})月(\d{1,2})日/,              // 2024年1月1日
        /(\d{1,2})月(\d{1,2})日/,                       // 1月1日
      ];

      for (const pattern of timePatterns) {
        const match = timeText.match(pattern);
        if (match) {
          const now = new Date();
          let year = now.getFullYear();
          let month = 1;
          let day = 1;
          let hour = 12;
          let minute = 0;

          if (pattern.source.includes('\\d{4}')) {
            // 包含年份
            year = parseInt(match[1] || '');
            month = parseInt(match[2] || '');
            day = parseInt(match[3] || '');
            if (match[4]) hour = parseInt(match[4]);
            if (match[5]) minute = parseInt(match[5]);
          } else {
            // 不包含年份，使用当前年份
            month = parseInt(match[1] || '');
            day = parseInt(match[2] || '');
          }

          const date = new Date(year, month - 1, day, hour, minute);
          return date.toISOString();
        }
      }
    } catch (error) {
      console.warn('时间解析失败:', error);
    }
    return null;
  }

  /**
   * 清理HTML内容，移除不必要的标签和样式
   * @param html 原始HTML内容
   * @returns 清理后的HTML内容
   */
  function cleanHtmlContent(html: string): string {
    // 移除script标签
    html = html.replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '');

    // 移除style标签
    html = html.replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '');

    // 移除内联样式中的一些属性（保留基本样式）
    html = html.replace(/style\s*=\s*"[^"]*?(font-size|color|text-align)[^"]*"/gi, (match) => {
      // 保留基本样式属性
      const basicStyles = match.match(/(font-size|color|text-align):\s*[^;"]*/g);
      if (basicStyles) {
        return `style="${basicStyles.join('; ')}"`;
      }
      return '';
    });

    // 移除一些微信特有的属性
    html = html.replace(/data-[^=]*="[^"]*"/gi, '');

    // 移除一些无用的属性
    html = html.replace(/(class|id)\s*=\s*"[^"]*"/gi, '');

    // 处理图片链接
    html = html.replace(/data-src="([^"]*)"/gi, 'src="$1"');

    // 移除空的标签
    html = html.replace(/<(\w+)[^>]*>\s*<\/\1>/gi, '');

    return html;
  }

  /**
   * 提取文章摘要
   * @param content HTML内容
   * @param maxLength 最大长度
   * @returns 文章摘要
   */
  function extractSummary(content: string, maxLength: number = 200): string {
    // 移除HTML标签
    const textContent = content.replace(/<[^>]*>/g, '');
    
    // 移除多余的空白字符
    const cleanText = textContent.replace(/\s+/g, ' ').trim();
    
    // 截取指定长度
    if (cleanText.length <= maxLength) {
      return cleanText;
    }
    
    return cleanText.substring(0, maxLength) + '...';
  }

  /**
   * 获取最后爬取的数据
   */
  function getLastCrawledData(): WechatArticleInfo | null {
    return lastCrawledData.value;
  }

  /**
   * 清除缓存数据
   */
  function clearCache(): void {
    lastCrawledData.value = null;
  }

  /**
   * 解析用户粘贴的内容
   * @param pastedContent 用户粘贴的HTML或文本内容
   * @param title 文章标题（可选）
   * @returns 解析后的文章信息
   */
  function parseUserPastedContent(pastedContent: string, title?: string): WechatArticleInfo {
    try {
      let content = pastedContent;
      let extractedTitle = title || '';

      // 如果内容包含HTML标签，说明是富文本内容
      if (/<[^>]+>/.test(content)) {
        // 尝试从HTML中提取标题
        if (!extractedTitle) {
          const titleMatch = content.match(/<h[1-6][^>]*>(.*?)<\/h[1-6]>/i);
          if (titleMatch && titleMatch[1]) {
            extractedTitle = titleMatch[1].replace(/<[^>]+>/g, '').trim();
          }
        }

        // 清理HTML内容
        content = cleanHtmlContent(content);
      } else {
        // 纯文本内容，转换为HTML格式
        content = content
          .split('\n')
          .filter(line => line.trim())
          .map(line => `<p>${line.trim()}</p>`)
          .join('\n');
      }

      // 生成摘要
      const summary = extractSummary(content, 200);

      return {
        title: extractedTitle || '请输入文章标题',
        content,
        author: '微信公众号',
        publish_time: new Date().toISOString(),
        summary,
      };
    } catch (error) {
      console.error('解析粘贴内容失败:', error);
      throw new Error('内容解析失败');
    }
  }

  /**
   * 从剪贴板获取内容
   * @returns 剪贴板内容
   */
  async function getClipboardContent(): Promise<string> {
    try {
      if (navigator.clipboard && navigator.clipboard.readText) {
        return await navigator.clipboard.readText();
      } else {
        throw new Error('浏览器不支持剪贴板API');
      }
    } catch (error) {
      console.warn('获取剪贴板内容失败:', error);
      throw new Error('无法获取剪贴板内容，请手动粘贴');
    }
  }

  return {
    // 状态
    loading,
    lastCrawledData,

    // 方法
    crawlArticle,
    isValidWechatUrl,
    crawlArticleClientSide,
    parseUserPastedContent,
    getClipboardContent,
    cleanHtmlContent,
    extractSummary,
    getLastCrawledData,
    clearCache,
  };
});
