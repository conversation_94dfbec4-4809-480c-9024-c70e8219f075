# 微信公众号文章爬取API文档

## 概述

微信公众号文章爬取功能提供了从微信公众号URL获取富文本内容的能力，支持提取文章标题、作者、内容、图片等完整信息。

## 特点

- ✅ **智能解析**: 自动识别和解析微信公众号文章结构
- ✅ **富文本支持**: 完整保留文章的HTML格式和样式
- ✅ **图片处理**: 自动处理文章中的图片链接
- ✅ **内容清理**: 移除不必要的脚本和样式，保留核心内容
- ✅ **错误处理**: 完善的异常处理和错误提示
- ✅ **URL验证**: 支持微信公众号URL格式验证

## API接口列表

### 🕷️ 爬取微信公众号文章

**接口地址**: `POST /article/crawl-wechat`

**功能**: 从微信公众号URL爬取文章的富文本内容

**认证要求**: ✅ 需要登录认证

**请求参数**:
```json
{
  "url": "https://mp.weixin.qq.com/s/xxxxxx"
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| url | string | 是 | 微信公众号文章URL |

**请求示例**:
```bash
curl -X POST "http://localhost:9000/article/crawl-wechat" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://mp.weixin.qq.com/s/abc123def456"
  }'
```

**返回字段**:
- `title`: 文章标题
- `author`: 作者信息
- `publish_time`: 发布时间
- `content`: 文章内容（富文本HTML）
- `summary`: 文章摘要（前200字符）
- `cover_image`: 封面图片URL
- `original_url`: 原始文章URL
- `crawl_time`: 爬取时间

**返回示例**:
```json
{
  "success": true,
  "data": {
    "title": "2024年人工智能发展趋势分析",
    "author": "科技前沿",
    "publish_time": "2024-01-15",
    "content": "<div class=\"rich_media_content\"><p>随着人工智能技术的快速发展...</p><img src=\"https://mmbiz.qpic.cn/mmbiz_jpg/xxx.jpg\" alt=\"AI发展图\"><p>未来AI将在以下领域取得突破...</p></div>",
    "summary": "随着人工智能技术的快速发展，2024年将是AI应用爆发的关键一年。本文深入分析了AI在各个领域的发展趋势，包括机器学习、深度学习、自然语言处理等核心技术的突破，以及在医疗、教育、金融等行业的具体应用场景...",
    "cover_image": "https://mmbiz.qpic.cn/mmbiz_jpg/cover123.jpg",
    "original_url": "https://mp.weixin.qq.com/s/abc123def456",
    "crawl_time": "2024-01-15T10:30:00"
  },
  "message": "微信文章爬取成功"
}
```

**错误返回示例**:
```json
{
  "success": false,
  "data": null,
  "message": "无效的微信公众号文章URL"
}
```

### ✅ 验证微信公众号URL

**接口地址**: `GET /article/validate-wechat-url`

**功能**: 验证URL是否为有效的微信公众号文章链接

**认证要求**: ✅ 需要登录认证

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| url | string | 是 | 待验证的URL |

**请求示例**:
```bash
curl -X GET "http://localhost:9000/article/validate-wechat-url?url=https://mp.weixin.qq.com/s/abc123" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**返回示例**:
```json
{
  "success": true,
  "data": {
    "url": "https://mp.weixin.qq.com/s/abc123",
    "is_valid": true,
    "message": "有效的微信公众号文章URL"
  },
  "message": "URL验证完成"
}
```

## 使用场景

### 1. 内容采集
```javascript
// 从微信公众号采集优质内容
const crawlResult = await fetch('/article/crawl-wechat', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    url: 'https://mp.weixin.qq.com/s/example'
  })
})

const article = await crawlResult.json()
if (article.success) {
  console.log('文章标题:', article.data.title)
  console.log('文章内容:', article.data.content)
}
```

### 2. 内容转换
```javascript
// 将爬取的内容转换为本地文章
async function importWeChatArticle(wechatUrl) {
  // 1. 爬取微信文章
  const crawlResponse = await fetch('/article/crawl-wechat', {
    method: 'POST',
    headers: {
      'Authorization': 'Bearer ' + token,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ url: wechatUrl })
  })
  
  const crawlResult = await crawlResponse.json()
  
  if (crawlResult.success) {
    // 2. 创建本地文章
    const createResponse = await fetch('/article/create', {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer ' + token,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        title: crawlResult.data.title,
        content: crawlResult.data.content,
        summary: crawlResult.data.summary,
        cover_image: crawlResult.data.cover_image,
        channel: '转载',
        style: '富文本'
      })
    })
    
    return await createResponse.json()
  }
}
```

### 3. URL验证
```javascript
// 在用户输入URL时进行验证
async function validateWeChatUrl(url) {
  const response = await fetch(`/article/validate-wechat-url?url=${encodeURIComponent(url)}`, {
    headers: {
      'Authorization': 'Bearer ' + token
    }
  })
  
  const result = await response.json()
  return result.data.is_valid
}

// 使用示例
const userInput = 'https://mp.weixin.qq.com/s/example'
const isValid = await validateWeChatUrl(userInput)

if (isValid) {
  console.log('URL有效，可以进行爬取')
} else {
  console.log('URL无效，请检查格式')
}
```

## 数据模型

### 请求模型

#### WeChatCrawlRequest
```python
class WeChatCrawlRequest(BaseModel):
    url: str  # 微信公众号文章URL
```

#### WeChatUrlValidation
```python
class WeChatUrlValidation(BaseModel):
    url: str         # 验证的URL
    is_valid: bool   # 是否为有效的微信公众号URL
    message: str     # 验证结果说明
```

### 响应模型

#### WeChatArticleData
```python
class WeChatArticleData(BaseModel):
    title: str         # 文章标题
    author: str        # 作者信息
    publish_time: str  # 发布时间
    content: str       # 文章内容（富文本HTML）
    summary: str       # 文章摘要
    cover_image: str   # 封面图片URL
    original_url: str  # 原始文章URL
    crawl_time: str    # 爬取时间
```

## 技术实现

### 架构设计

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   API Router    │───▶│   Schemas        │───▶│  Crawler Tool   │
│                 │    │                  │    │                 │
│ - 接口定义      │    │ - 数据模型       │    │ - 爬取逻辑      │
│ - 参数验证      │    │ - 数据验证       │    │ - 内容解析      │
│ - 响应格式化    │    │ - 类型定义       │    │ - 错误处理      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### 爬取流程

1. **请求验证**: 使用Pydantic模型验证请求参数
2. **URL验证**: 检查是否为有效的微信公众号URL
3. **发送请求**: 使用模拟浏览器请求获取网页内容
4. **内容解析**: 使用BeautifulSoup解析HTML结构
5. **数据提取**: 提取标题、作者、内容、图片等信息
6. **内容清理**: 移除不必要的标签和属性
7. **图片处理**: 处理图片链接，确保可访问性
8. **数据验证**: 使用schemas模型验证返回数据
9. **返回结果**: 格式化返回爬取的数据

### 核心技术

- **Pydantic**: 数据模型定义和验证
- **BeautifulSoup**: HTML解析和内容提取
- **Requests**: HTTP请求和会话管理
- **正则表达式**: URL格式验证和内容匹配
- **异常处理**: 完善的错误处理机制

## 注意事项

### 1. 使用限制
- 需要用户登录认证
- 仅支持公开可访问的微信公众号文章
- 部分文章可能需要在微信客户端中打开

### 2. 技术限制
- 微信可能会更新页面结构，影响爬取效果
- 网络环境可能影响爬取成功率
- 图片链接可能存在防盗链限制

### 3. 合规建议
- 仅用于学习和研究目的
- 尊重原作者版权
- 遵守相关法律法规
- 建议在使用前获得内容授权

### 4. 性能考虑
- 爬取过程可能需要几秒钟时间
- 建议添加适当的超时处理
- 可以考虑异步处理大量爬取任务

## 错误处理

### 常见错误类型

| 错误类型 | 状态码 | 说明 | 解决方案 |
|----------|--------|------|----------|
| URL格式错误 | 400 | 不是有效的微信公众号URL | 检查URL格式 |
| 网络超时 | 400 | 请求超时 | 检查网络连接，重试 |
| 内容受限 | 400 | 文章需要在微信中打开 | 尝试其他文章 |
| 解析失败 | 400 | 无法解析文章内容 | 检查文章是否正常 |
| 认证失败 | 401 | 用户未登录 | 提供有效的认证token |

### 错误处理示例

```javascript
async function safeCrawlWeChatArticle(url) {
  try {
    const response = await fetch('/article/crawl-wechat', {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer ' + token,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ url })
    })
    
    const result = await response.json()
    
    if (result.success) {
      return result.data
    } else {
      throw new Error(result.message)
    }
  } catch (error) {
    console.error('爬取失败:', error.message)
    
    // 根据错误类型进行处理
    if (error.message.includes('URL')) {
      alert('请输入有效的微信公众号文章链接')
    } else if (error.message.includes('超时')) {
      alert('网络请求超时，请重试')
    } else if (error.message.includes('微信客户端')) {
      alert('该文章需要在微信中打开，无法直接爬取')
    } else {
      alert('爬取失败，请稍后重试')
    }
    
    return null
  }
}
```

## 安装依赖

使用此功能需要安装以下Python包：

```bash
pip install beautifulsoup4==4.12.2
pip install lxml==4.9.3
pip install requests==2.31.0
```

或使用requirements文件：

```bash
pip install -r requirements_wechat_crawler.txt
```
