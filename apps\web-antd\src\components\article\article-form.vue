<script setup lang="ts">
import { computed, ref, watch, onMounted } from 'vue';
import { 
  Form, 
  Input, 
  Select, 
  DatePicker, 
  Switch, 
  Button,
  Row,
  Col,
  Card,
  message 
} from 'ant-design-vue';
import dayjs from 'dayjs';

// 导入组件
import RichEditor from './rich-editor.vue';
import ImageUpload from '#/components/image/image-upload.vue';
import WechatCrawlerModal from './wechat-crawler-modal.vue';

// 导入stores
import { useArticleAuthorStore } from '#/store/article/author';
import { useArticleTagStore } from '#/store/article/tag';
import { useWechatCrawlerStore } from '#/store/article/wechat-crawler';

// 导入类型和常量
import type { ArticleFormData } from '#/types';
import type { WechatArticleInfo } from '#/store/article/wechat-crawler';
import {
  ARTICLE_STYLE_OPTIONS,
  ARTICLE_CHANNEL_OPTIONS,
  ARTICLE_STATUS_OPTIONS
} from '#/types';

defineOptions({
  name: 'ArticleForm',
});

const props = defineProps<{
  loading?: boolean;
  isEdit?: boolean;
}>();

const emit = defineEmits<{
  submit: [formData: ArticleFormData];
  cancel: [];
}>();

// 使用stores
const authorStore = useArticleAuthorStore();
const tagStore = useArticleTagStore();
const crawlerStore = useWechatCrawlerStore();

// 表单引用
const formRef = ref();

// 微信爬取弹窗状态
const showWechatCrawler = ref(false);

// 表单数据
const formData = ref<ArticleFormData>({
  title: '',
  style: '富文本',
  content: '',
  channel: '头条',
  cover_image: '',
  summary: '',
  share_image: '',
  publish_time: dayjs().toISOString(),
  is_visible: true,
  seo_title: '',
  seo_keywords: '',
  seo_description: '',
  status: '1', // 默认待审核
  author_id: 0,
  tag_ids: [],
});

// 作者选项
const authorOptions = ref<Array<{ label: string; value: number }>>([]);

// 标签选项
const tagOptions = ref<Array<{ label: string; value: number }>>([]);

// 发布时间的dayjs对象，用于DatePicker
const publishTimeValue = computed({
  get() {
    return formData.value.publish_time ? dayjs(formData.value.publish_time) : undefined;
  },
  set(value: any) {
    formData.value.publish_time = value ? value.toISOString() : '';
  }
});

// 表单验证规则
const formRules: any = {
  title: [
    { required: true, message: '请输入文章标题', trigger: 'blur' },
    { max: 100, message: '标题不能超过100个字符', trigger: 'blur' },
  ],
  style: [
    { required: true, message: '请选择文章风格', trigger: 'change' },
  ],
  content: [
    { required: true, message: '请输入文章内容', trigger: 'blur' },
  ],
  channel: [
    { required: true, message: '请选择发布频道', trigger: 'change' },
  ],
  publish_time: [
    { required: true, message: '请选择发布时间', trigger: 'change' },
  ],
  status: [
    { required: true, message: '请选择文章状态', trigger: 'change' },
  ],
  author_id: [
    { required: true, message: '请选择文章作者', trigger: 'change' },
  ],
  tag_ids: [
    { required: true, message: '请选择文章标签', trigger: 'change' },
    { type: 'array', min: 1, message: '至少选择一个标签', trigger: 'change' },
  ],
  summary: [
    { max: 300, message: '简介不能超过300个字符', trigger: 'blur' },
  ],
  seo_title: [
    { max: 100, message: 'SEO标题不能超过100个字符', trigger: 'blur' },
  ],
  seo_keywords: [
    { max: 100, message: 'SEO关键字不能超过100个字符', trigger: 'blur' },
  ],
  seo_description: [
    { max: 300, message: 'SEO描述不能超过300个字符', trigger: 'blur' },
  ],
};

// 加载作者列表
async function loadAuthors() {
  try {
    await authorStore.getArticleAuthorList({ page: 1, page_size: 100 });
    authorOptions.value = authorStore.authors.map(author => ({
      label: `${author.author_name} (${author.wechat_nickname})`,
      value: author.id!,
    }));
  } catch (error) {
    message.error('加载作者列表失败');
  }
}

// 加载标签列表
async function loadTags() {
  try {
    await tagStore.getArticleTagList({ page: 1, page_size: 100 });
    tagOptions.value = tagStore.tags.map(tag => ({
      label: tag.name,
      value: tag.id!,
    }));
  } catch (error) {
    message.error('加载标签列表失败');
  }
}

// 处理表单提交
async function handleSubmit() {
  try {
    await formRef.value?.validate();
    
    // 转换发布时间格式
    const submitData = {
      ...formData.value,
      publish_time: dayjs(formData.value.publish_time).toISOString(),
    };
    
    emit('submit', submitData);
  } catch (error) {
    console.error('表单验证失败:', error);
  }
}

// 处理取消
function handleCancel() {
  emit('cancel');
}

// 重置表单
function resetForm() {
  formData.value = {
    title: '',
    style: '富文本',
    content: '',
    channel: '头条',
    cover_image: '',
    summary: '',
    share_image: '',
    publish_time: dayjs().toISOString(),
    is_visible: true,
    seo_title: '',
    seo_keywords: '',
    seo_description: '',
    status: '1',
    author_id: 0,
    tag_ids: [],
  };
  formRef.value?.resetFields();
}

// 设置表单数据（用于编辑模式）
function setFormData(data: ArticleFormData) {
  formData.value = { ...data };
  // 更新发布时间的dayjs对象
  publishTimeValue.value = dayjs(data.publish_time);
}

// 显示微信爬取弹窗
function showWechatCrawlerModal() {
  showWechatCrawler.value = true;
}

// 处理微信文章爬取成功
function handleWechatCrawlSuccess(data: WechatArticleInfo) {
  try {
    // 填充表单数据
    if (data.title) {
      formData.value.title = data.title;
    }

    if (data.content) {
      formData.value.content = data.content;
    }

    if (data.summary) {
      formData.value.summary = data.summary;
    } else if (data.content) {
      // 如果没有摘要，从内容中提取
      formData.value.summary = crawlerStore.extractSummary(data.content, 200);
    }

    // 如果有封面图片，设置封面
    if (data.cover_image) {
      formData.value.cover_image = data.cover_image;
    }

    message.success('微信文章内容已成功导入到编辑器！');
  } catch (error) {
    console.error('处理爬取数据失败:', error);
    message.error('导入文章内容时出现错误');
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadAuthors();
  loadTags();
});

// 暴露方法给父组件
defineExpose({
  resetForm,
  setFormData,
});
</script>

<template>
  <div class="article-form">
    <Form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      layout="vertical"
      :scroll-to-first-error="true"
    >
      <Row :gutter="24">
        <!-- 左侧主要内容 -->
        <Col :span="18">
          <Card title="基本信息" class="mb-6">
            <Form.Item label="文章标题" name="title">
              <Input
                v-model:value="formData.title"
                placeholder="请输入文章标题（最多100个字符）"
                :maxlength="100"
                show-count
              />
            </Form.Item>

            <Row :gutter="16">
              <Col :span="12">
                <Form.Item label="文章风格" name="style">
                  <Select
                    v-model:value="formData.style"
                    placeholder="请选择文章风格"
                    :options="ARTICLE_STYLE_OPTIONS"
                  />
                </Form.Item>
              </Col>
              <Col :span="12">
                <Form.Item label="发布频道" name="channel">
                  <Select
                    v-model:value="formData.channel"
                    placeholder="请选择发布频道"
                    :options="ARTICLE_CHANNEL_OPTIONS"
                  />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item name="content">
              <template #label>
                <div class="flex items-center justify-between w-full">
                  <span>文章内容</span>
                  <Button
                    type="link"
                    size="small"
                    @click="showWechatCrawlerModal"
                  >
                    📱 微信文章导入
                  </Button>
                </div>
              </template>
              <RichEditor
                v-model="formData.content"
                placeholder="请输入文章内容..."
                :height="500"
              />
            </Form.Item>

            <Form.Item label="文章简介" name="summary">
              <Input.TextArea
                v-model:value="formData.summary"
                placeholder="请输入文章简介（最多300个字符）"
                :maxlength="300"
                :rows="4"
                show-count
              />
            </Form.Item>
          </Card>

          <!-- 封面设置 - 横向布局 -->
          <Card title="封面设置" class="mb-6">
            <Row :gutter="24">
              <Col :span="12">
                <Form.Item label="文章封面" name="cover_image">
                  <ImageUpload
                    v-model="formData.cover_image"
                    placeholder="上传文章封面"
                    category="article"
                  />
                </Form.Item>
              </Col>
              <Col :span="12">
                <Form.Item label="分享封面" name="share_image">
                  <ImageUpload
                    v-model="formData.share_image"
                    placeholder="上传分享封面"
                    category="article"
                  />
                </Form.Item>
              </Col>
            </Row>
          </Card>
        </Col>

        <!-- 右侧设置 -->
        <Col :span="6">
          <Card title="发布设置" class="mb-6">
            <Form.Item label="文章作者" name="author_id">
              <Select 
                v-model:value="formData.author_id" 
                placeholder="请选择文章作者"
                :options="authorOptions"
                :loading="authorStore.loading"
                show-search
                :filter-option="(input: string, option: any) => 
                  option.label.toLowerCase().includes(input.toLowerCase())
                "
              />
            </Form.Item>
            
            <Form.Item label="文章标签" name="tag_ids">
              <Select 
                v-model:value="formData.tag_ids" 
                mode="multiple"
                placeholder="请选择文章标签"
                :options="tagOptions"
                :loading="tagStore.loading"
                show-search
                :filter-option="(input: string, option: any) => 
                  option.label.toLowerCase().includes(input.toLowerCase())
                "
              />
            </Form.Item>
            
            <Form.Item label="发布时间" name="publish_time">
              <DatePicker
                v-model:value="publishTimeValue"
                show-time
                format="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择发布时间"
                class="w-full"
              />
            </Form.Item>
            
            <Form.Item label="文章状态" name="status">
              <Select 
                v-model:value="formData.status" 
                placeholder="请选择文章状态"
                :options="ARTICLE_STATUS_OPTIONS"
              />
            </Form.Item>
            
            <Form.Item label="是否可见" name="is_visible">
              <Switch 
                v-model:checked="formData.is_visible"
                checked-children="可见"
                un-checked-children="隐藏"
              />
            </Form.Item>
          </Card>
          
          <Card title="SEO设置" class="mb-6">
            <Form.Item label="SEO标题" name="seo_title">
              <Input 
                v-model:value="formData.seo_title" 
                placeholder="请输入SEO标题（最多100个字符）"
                :maxlength="100"
                show-count
              />
            </Form.Item>
            
            <Form.Item label="SEO关键字" name="seo_keywords">
              <Input 
                v-model:value="formData.seo_keywords" 
                placeholder="请输入SEO关键字，多个关键字用逗号分隔"
                :maxlength="100"
                show-count
              />
            </Form.Item>
            
            <Form.Item label="SEO描述" name="seo_description">
              <Input.TextArea 
                v-model:value="formData.seo_description" 
                placeholder="请输入SEO描述（最多300个字符）"
                :maxlength="300"
                :rows="3"
                show-count
              />
            </Form.Item>
          </Card>
        </Col>
      </Row>
      
      <!-- 操作按钮 -->
      <div class="mt-6 text-center">
        <Button 
          size="large" 
          class="mr-4"
          @click="handleCancel"
        >
          取消
        </Button>
        <Button
          type="primary"
          size="large"
          :loading="loading"
          @click="handleSubmit"
        >
          {{ isEdit ? '更新文章' : '发布文章' }}
        </Button>
      </div>
    </Form>

    <!-- 微信文章爬取弹窗 -->
    <WechatCrawlerModal
      v-model:visible="showWechatCrawler"
      @crawl-success="handleWechatCrawlSuccess"
    />
  </div>
</template>

<style scoped>
.article-form {
  max-width: 1200px;
  margin: 0 auto;
}

:deep(.ant-card-head-title) {
  font-weight: 600;
}

:deep(.ant-form-item-label > label) {
  font-weight: 500;
}
</style>
