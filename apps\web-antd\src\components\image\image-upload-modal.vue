<script setup lang="ts">
import { ref, computed } from 'vue';
import { 
  Modal, 
  Upload, 
  Button, 
  Select, 
  Form, 
  message,
  Progress,
  Space 
} from 'ant-design-vue';
import { Upload as UploadIcon, X } from 'lucide-vue-next';

// 导入store
import { useImageStore } from '#/store/image/image';

// 导入类型和常量
import type { ImageInfo } from '#/types/image';
import { SUPPORTED_IMAGE_TYPES, MAX_IMAGE_SIZE } from '#/types/image';

defineOptions({
  name: 'ImageUploadModal',
});

const props = defineProps<{
  visible: boolean;
}>();

const emit = defineEmits<{
  'update:visible': [visible: boolean];
  'upload-success': [image: ImageInfo];
}>();

const imageStore = useImageStore();

// 组件状态
const uploading = ref(false);
const uploadProgress = ref(0);
const selectedTags = ref<number[]>([]);
const previewFiles = ref<File[]>([]);

// 计算属性
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
});

const tagOptions = computed(() => 
  imageStore.tags.map(tag => ({
    label: tag.name,
    value: tag.id,
  }))
);

const hasFiles = computed(() => previewFiles.value.length > 0);

// 处理文件选择
function handleFileChange(info: any) {
  const { fileList } = info;
  previewFiles.value = fileList.map((file: any) => file.originFileObj || file).filter(Boolean);
}

// 移除文件
function removeFile(index: number) {
  previewFiles.value.splice(index, 1);
}

// 验证文件
function validateFile(file: File): boolean {
  // 验证文件类型
  if (!SUPPORTED_IMAGE_TYPES.includes(file.type)) {
    message.error(`不支持的图片格式：${file.name}`);
    return false;
  }

  // 验证文件大小
  if (file.size > MAX_IMAGE_SIZE) {
    message.error(`图片大小不能超过 10MB：${file.name}`);
    return false;
  }

  return true;
}

// 上传文件
async function handleUpload() {
  if (previewFiles.value.length === 0) {
    message.error('请选择要上传的图片');
    return;
  }

  try {
    uploading.value = true;
    uploadProgress.value = 0;

    const uploadPromises = previewFiles.value.map(async (file, index) => {
      if (!validateFile(file)) {
        return null;
      }

      try {
        const imageInfo = await imageStore.uploadImage({
          file,
          original_name: file.name,
          category: 'library',
          is_public: true,
          tag_ids: selectedTags.value.length > 0 ? selectedTags.value.join(',') : undefined,
        });

        // 更新进度
        uploadProgress.value = Math.round(((index + 1) / previewFiles.value.length) * 100);
        
        return imageInfo;
      } catch (error) {
        message.error(`上传失败：${file.name}`);
        return null;
      }
    });

    const results = await Promise.all(uploadPromises);
    const successCount = results.filter(Boolean).length;

    if (successCount > 0) {
      message.success(`成功上传 ${successCount} 张图片`);
      
      // 触发上传成功事件
      results.forEach(result => {
        if (result) {
          emit('upload-success', result);
        }
      });
      
      // 重置状态
      handleReset();
      modalVisible.value = false;
    }
  } catch (error) {
    message.error('上传失败，请重试');
  } finally {
    uploading.value = false;
    uploadProgress.value = 0;
  }
}

// 重置状态
function handleReset() {
  previewFiles.value = [];
  selectedTags.value = [];
  uploadProgress.value = 0;
}

// 取消上传
function handleCancel() {
  handleReset();
  modalVisible.value = false;
}

// 自定义上传行为
function customRequest() {
  // 阻止默认上传行为
  return false;
}

// 格式化文件大小
function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}
</script>

<template>
  <Modal
    v-model:open="modalVisible"
    title="上传图片"
    width="600px"
    @cancel="handleCancel"
  >
    <div class="upload-container">
      <!-- 文件选择区域 -->
      <div class="upload-area">
        <Upload.Dragger
          :multiple="true"
          :show-upload-list="false"
          accept="image/*"
          :custom-request="customRequest"
          @change="handleFileChange"
        >
          <div class="upload-content">
            <UploadIcon :size="48" class="upload-icon" />
            <p class="upload-text">点击或拖拽图片到此区域上传</p>
            <p class="upload-hint">支持 JPG、PNG、GIF、WebP 格式，单个文件不超过 10MB</p>
          </div>
        </Upload.Dragger>
      </div>

      <!-- 文件预览列表 -->
      <div v-if="hasFiles" class="file-list">
        <h4 class="list-title">待上传文件 ({{ previewFiles.length }})</h4>
        <div class="file-items">
          <div
            v-for="(file, index) in previewFiles"
            :key="index"
            class="file-item"
          >
            <div class="file-info">
              <span class="file-name">{{ file.name }}</span>
              <span class="file-size">{{ formatFileSize(file.size) }}</span>
            </div>
            <Button
              type="text"
              size="small"
              danger
              @click="removeFile(index)"
            >
              <template #icon>
                <X :size="14" />
              </template>
            </Button>
          </div>
        </div>
      </div>

      <!-- 标签选择 -->
      <div class="tag-selection">
        <Form.Item label="关联标签">
          <Select
            v-model:value="selectedTags"
            mode="multiple"
            placeholder="请选择要关联的标签（可选）"
            :options="tagOptions"
            allow-clear
          />
        </Form.Item>
      </div>

      <!-- 上传进度 -->
      <div v-if="uploading" class="upload-progress">
        <Progress :percent="uploadProgress" />
        <p class="progress-text">正在上传中...</p>
      </div>
    </div>

    <template #footer>
      <Space>
        <Button @click="handleCancel" :disabled="uploading">
          取消
        </Button>
        <Button @click="handleReset" :disabled="uploading || !hasFiles">
          重置
        </Button>
        <Button 
          type="primary" 
          @click="handleUpload"
          :loading="uploading"
          :disabled="!hasFiles"
        >
          开始上传
        </Button>
      </Space>
    </template>
  </Modal>
</template>

<style scoped>
.upload-container {
  padding: 16px 0;
}

.upload-area {
  margin-bottom: 24px;
}

.upload-content {
  padding: 40px 20px;
  text-align: center;
}

.upload-icon {
  color: #d9d9d9;
  margin-bottom: 16px;
}

.upload-text {
  font-size: 16px;
  color: #666;
  margin-bottom: 8px;
}

.upload-hint {
  font-size: 14px;
  color: #999;
}

.file-list {
  margin-bottom: 24px;
}

.list-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;
}

.file-items {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0;
}

.file-item:last-child {
  border-bottom: none;
}

.file-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.file-name {
  font-size: 14px;
  color: #333;
  word-break: break-all;
}

.file-size {
  font-size: 12px;
  color: #999;
}

.tag-selection {
  margin-bottom: 24px;
}

.upload-progress {
  text-align: center;
}

.progress-text {
  margin-top: 8px;
  font-size: 14px;
  color: #666;
}

:deep(.ant-upload-drag) {
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  background: #fafafa;
  transition: border-color 0.3s;
}

:deep(.ant-upload-drag:hover) {
  border-color: #1890ff;
}

:deep(.ant-upload-drag.ant-upload-drag-hover) {
  border-color: #1890ff;
}
</style>
