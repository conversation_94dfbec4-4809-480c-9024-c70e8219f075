【文章相关】
参考接口文档 API_Documentation_ArticleManagement.md 完成文章、标签、作者、标签分类的接口对接开发，包括页面实现、接口数据对接，
并通过在view、components、store目录创建文章子目录方式存放相关页面、组件，页面组件要做到尽可能的拆分、代码解耦、接口对接通过编写独立的ts文件实现，
可参考现有的siteconfig对象的管理实现方式。同时在系统左侧增加文章管理的一级菜单，包括：文章发布、文章列表、标签、作者、标签分类的二级菜单

一、实现标签分类管理的对接开发，完成以下工作
1、开发标签分类管理的列表页面、新增/编辑弹窗组件、接口对接的ts文件
2、页面与二级菜单标签分类进行路由对应
3、列表页面内默认列表分页展示已有数据、右上角点击新增弹窗方式新增、列表页支持编辑，编辑时候可以服用新增弹窗
4、列表支持删除操作，删除需要确认

二、实现标签管理的对接开发，完成以下工作
1、开发标签管理的列表页面、新增/编辑弹窗组件、接口对接的ts文件
2、页面与二级菜单标签进行路由对应
3、列表页面内默认列表分页展示已有数据、右上角点击新增弹窗方式新增、列表页支持编辑，编辑时候可以服用新增弹窗
4、列表支持删除操作，删除需要确认

三、实现文章作者管理的对接开发，完成以下工作
1、开发文章作者管理的列表页面、新增/编辑弹窗组件、接口对接的ts文件
2、页面与二级菜单作者进行路由对应
3、列表页面内默认列表分页展示已有数据、右上角点击新增弹窗方式新增、列表页支持编辑，编辑时候可以服用新增弹窗
4、列表支持删除操作，删除需要确认

以上管理页面的开发可以参考现有的header-settings.vue页面中的实现



1、点击左侧菜单的标签分类，无法打开对应的页面
2、标签、作者的列表展示页面上，带有图标的操作按钮，文字会超出按钮，显示不美观，请修复
3、检查标签分类的页面是否同样存在2的问题

文章发布实现
请学习网页 https://www.wangeditor.com/v5/for-frame.html#vue3 通过引入wangEditor富文本编辑器，实现文章发布页面，
可以参考已实现的文章作者管理方式，将组件、接口请求等做好拆分解耦
1、增加wangEditor富文本编辑器的依赖
2、修改publish.vue文件，实现文章发布页面
3、参考API_Documentation_ArticleManagement.md接口文档，实现文章发布接口的接口开发，并将接口内容封装到store/article 目录下

图片上传
参考后台已有的图片接口文档API_Documentation_ImageManagement.md 修改文章发布页面的内容
1、富文本图片上传修改使用接口文档中接口上传
2、开发图片管理的组件弹窗，列表分页展示所有图片（图片为主，名称为次）。可以通过文件原始名称搜索、通过标签下拉过滤、上传本地文件、新增标签
3、文章封面、分享封面改为上传图片的组件，鼠标移动到组件上时候，显示本地上传或图片库，本地上传直接调用接口文档上传，成功后按照图片地址回显。选择图片库
就打开图片管理的组件弹窗，选择图片后回显图片地址


文章列表页面和接口实现
1、按照API_Documentation_ArticleManagement.md接口文档实现文章列表页面开发，支持按照文章标题、作者、发布时间（起始）、标签、审核状态过滤筛选
2、列表展示封面图、标题、作者、标签、状态、、是否展示（可以快捷操作更新）、阅读数（支持快捷修改）、收藏数、点赞数、评论数、发表时间
3、列表操作列需要支持编辑、查看评论、删除操作，编辑文章复用现有的文章发布页面


图片上传弹窗组件需要修改以下几点
1、右上角的搜索。重置、上传、新增标签等文字与按钮的图片没有排列整齐，需要优化
2、筛选条件输入框前面增加说明，例如：标题：输入款，标签：请选择标签等
3、筛选条件中移除分类的筛选条件
4、标签筛选的下拉数据应该从图片库弹窗标签新增那里读取，图片标签的新增采用二级弹窗方式输入
5、图片库的图片上传，采用二级弹窗方式，上传图片的同时可以选择关联的标签有哪些

【微信公众号爬取】
1、开发一个通过微信公众号爬取富文本内容的工具类，保存到store/article 目录下，可以通过输入微信公众号url地址，直接获取内容的富文本
2、修改文章发布的页面，增加微信公众号抓取按钮，点击后弹窗输入微信公众号文章网址，点击确定后，调用抓取工具类获取富文本内容，获取到直接复制到富文本的输入框，如果获取失败，给出友好提示
3、新开发的功能，与现有功能尽量独立、解耦，已有的代码逻辑尽可能少的侵入，保持优雅


参考接口文档 WECHAT_CRAWLER_API.md 修改文章发布，从微信公众号提取内容的部分
1、调用后台接口方式获取指定连接的富文本内容，接口请求参考现有的store中方式进行封装实现，降低系统耦合，设计优雅
2、首先校验url通过接口，只有合法url才可以获取
