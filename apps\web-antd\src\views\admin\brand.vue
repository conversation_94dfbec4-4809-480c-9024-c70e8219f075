<script lang="ts" setup>
import { ref } from 'vue';

import { Button, Input, List } from 'ant-design-vue';
import { Search } from 'lucide-vue-next';

import { useBrandStore } from '#/store/brand';
import HourLivePage from '#/views/template/common.vue';

// types
import type { WIPOObject } from '#/types';

const brandStore = useBrandStore();
const searchKeyword = ref('');
const loading = ref(false);
const imageCache = ref<Record<string, string>>({}); // 用于缓存图片base64数据

// 获取图片base64数据的方法
const getImageBase64 = async (item: WIPOObject) => {
  const cacheKey = `${item.collection}_${item.st13}`;

  // 如果已经缓存，直接返回
  if (imageCache.value[cacheKey]) {
    return imageCache.value[cacheKey];
  }

  try {
    const imageUrl = `https://images.branddb.wipo.int/brands/${item.collection}/${item.st13}/${item.logo?.[0]}-th.jpg`;
    const base64Data = await brandStore.imageBase64Data({
      // hashsearch: '69022027-b7f7-f2e6-7af7-9748b96b58e8',
      // hashsearch: item.hashsearch || '',
      hashsearch: 'b9d8b0c9-3707-49ee-8267-860a6ef895b1',
      image_url: imageUrl,
    });
    // 缓存结果
    imageCache.value[cacheKey] = base64Data;
    return base64Data;
  } catch (error) {
    console.error('获取图片失败:', error);
    return '';
  }
};

const handleSearch = async () => {
  if (!searchKeyword.value) return;
  loading.value = true;
  try {
    await brandStore.brandquery({ name: searchKeyword.value });
    console.log(brandStore.brands);
  } finally {
    loading.value = false;
  }
};
</script>

<template>
  <HourLivePage :content-overflow="true">
    <template #header>
      <div class="flex items-center gap-4 p-4">
        <Input
          v-model:value="searchKeyword"
          :placeholder="$t('请输入品牌名称')"
          class="w-64"
          @press-enter="handleSearch"
        >
          <template #suffix>
            <Search class="h-4 w-4 cursor-pointer" @click="handleSearch" />
          </template>
        </Input>
        <Button :loading="loading" type="primary" @click="handleSearch">
          {{ $t('查询') }}
        </Button>
      </div>
    </template>

    <template #content>
      <div class="p-4">
        <List
          :data-source="brandStore.brands"
          :loading="loading"
          item-layout="horizontal"
        >
          <template #renderItem="{ item }">
            <List.Item>
              <List.Item.Meta>
                <template #avatar>
                  <img
                    v-if="item.collection && item.st13"
                    :src="imageCache[`${item.collection}_${item.st13}`] || ''"
                    alt="brand logo"
                    class="h-16 w-16 object-contain"
                    @error="getImageBase64(item)"
                  />
                </template>
                <template #title>
                  <div class="text-lg font-medium">
                    {{ item.brandName?.[0] }}
                  </div>
                </template>
                <template #description>
                  <div class="space-y-1">
                    <div>申请号：{{ item.applicationNumber }}</div>
                    <div>申请人：{{ item.applicant?.[0] }}</div>
                    <div>申请日期：{{ item.applicationDate }}</div>
                    <div>状态：{{ item.status }}</div>
                  </div>
                </template>
              </List.Item.Meta>
            </List.Item>
          </template>
        </List>
      </div>
    </template>
  </HourLivePage>
</template>

<style scoped>
.scroller {
  height: 100%;
}

:deep(.vue-recycle-scroller__item-wrapper) {
  padding: 16px;
  padding-bottom: 0;
}

:deep(.vue-recycle-scroller__item-wrapper:last-child) {
  padding-bottom: 16px;
}
</style>
