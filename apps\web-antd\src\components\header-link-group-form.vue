<script setup lang="ts">
import { reactive, ref, watch } from 'vue';
import { Form, FormItem, Input, Modal } from 'ant-design-vue';

defineOptions({
  name: 'HeaderLinkGroupForm',
});

const props = defineProps<{
  visible: boolean;
  editingIndex: number;
  editingData?: { name: string; url: string } | null;
}>();

const emit = defineEmits<{
  'update:visible': [value: boolean];
  cancel: [];
  confirm: [formData: any, index: number];
}>();

const formRef = ref();
const formData = reactive({
  name: '',
  url: '',
});

const formRules = {
  name: [{ message: '请输入链接名称', required: true, trigger: 'blur' }],
  url: [
    { message: '请输入链接地址', required: true, trigger: 'blur' },
    { message: '请输入有效的URL地址', type: 'url', trigger: 'blur' },
  ],
};

// 重置表单
function resetForm() {
  formRef.value?.resetFields();
  Object.assign(formData, {
    name: '',
    url: '',
  });
}

// 监听visible属性变化
watch(() => props.visible, (newVal) => {
  if (!newVal) {
    // 当弹窗关闭时，重置表单
    resetForm();
  }
});

// 监听编辑数据变化，回显数据
watch(() => props.editingData, (newData) => {
  if (newData && props.editingIndex !== -1) {
    // 编辑模式，回显数据
    Object.assign(formData, {
      name: newData.name || '',
      url: newData.url || '',
    });
  } else {
    // 新增模式，重置表单
    resetForm();
  }
}, { immediate: true });

// 确认
async function handleConfirm() {
  try {
    await formRef.value?.validate();
    emit('confirm', formData, props.editingIndex);
  } catch (error) {
    console.error('表单验证失败:', error);
  }
}

// 取消
function handleCancel() {
  emit('update:visible', false);
  emit('cancel');
}

// 添加关闭弹窗的方法
function handleClose() {
  emit('update:visible', false);
  emit('cancel');
}
</script>

<template>
  <Modal
    :ok-text="editingIndex === -1 ? '添加' : '更新'"
    :title="editingIndex === -1 ? '添加链接' : '编辑链接'"
    :visible="visible"
    :destroyOnClose="true"
    width="500px"
    @cancel="handleCancel"
    @close="handleClose"
    @ok="handleConfirm"
  >
    <Form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      class="mt-4"
      layout="vertical"
    >
      <FormItem label="链接名称" name="name" required>
        <Input v-model:value="formData.name" placeholder="请输入链接名称" />
      </FormItem>
      <FormItem label="链接地址" name="url" required>
        <Input v-model:value="formData.url" placeholder="请输入链接地址" />
      </FormItem>
    </Form>
  </Modal>
</template>



