<script setup lang="ts">
import { But<PERSON>, Popconfirm, Table, Avatar } from 'ant-design-vue';
import { Edit, Trash2, User } from 'lucide-vue-next';
import type { ArticleAuthor } from '#/types';

defineOptions({
  name: 'AuthorTable',
});

const props = defineProps<{
  authors: ArticleAuthor[];
  loading: boolean;
  totalRecords: number;
  currentPage: number;
  pageSize: number;
}>();

const emit = defineEmits<{
  edit: [record: ArticleAuthor];
  delete: [id: number];
  pageChange: [page: number, pageSize: number];
}>();

// 表格列配置
const columns = [
  {
    dataIndex: 'wechat_nickname',
    key: 'wechat_nickname',
    title: '微信昵称',
    width: 200,
  },
  {
    dataIndex: 'author_name',
    key: 'author_name',
    title: '作者名',
    width: 150,
  },
  {
    dataIndex: 'create_time',
    key: 'create_time',
    title: '创建时间',
    width: 180,
  },
  {
    dataIndex: 'update_time',
    key: 'update_time',
    title: '更新时间',
    width: 180,
  },
  {
    align: 'center' as const,
    key: 'action',
    title: '操作',
    width: 120,
    fixed: 'right' as const,
  },
];

// 格式化时间
function formatTime(time?: string) {
  if (!time) return '-';
  return new Date(time).toLocaleString('zh-CN');
}

// 处理分页变化
function handlePageChange(page: number, pageSize: number) {
  emit('pageChange', page, pageSize);
}

// 获取头像显示文字
function getAvatarText(name: string) {
  if (!name) return 'A';
  return name.charAt(0).toUpperCase();
}

// 获取头像颜色
function getAvatarColor(name: string) {
  const colors = ['#f56a00', '#7265e6', '#ffbf00', '#00a2ae', '#87d068', '#108ee9'];
  const charCode = name.charCodeAt(0);
  return colors[charCode % colors.length];
}
</script>

<template>
  <Table
    :columns="columns"
    :data-source="authors"
    :loading="loading"
    :pagination="{
      current: currentPage,
      pageSize: pageSize,
      total: totalRecords,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total: number, range: [number, number]) =>
        `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
      pageSizeOptions: ['10', '20', '50', '100'],
    }"
    row-key="id"
    scroll="{ x: 800 }"
    @change="(pagination: any) => handlePageChange(pagination.current, pagination.pageSize)"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.key === 'wechat_nickname'">
        <div class="flex items-center gap-3">
          <Avatar 
            :style="{ backgroundColor: getAvatarColor(record.wechat_nickname || '') }"
            :size="32"
          >
            {{ getAvatarText(record.wechat_nickname || '') }}
          </Avatar>
          <span class="font-medium">{{ record.wechat_nickname }}</span>
        </div>
      </template>
      <template v-else-if="column.key === 'author_name'">
        <div class="flex items-center gap-2">
          <User :size="16" class="text-gray-400" />
          <span>{{ record.author_name }}</span>
        </div>
      </template>
      <template v-else-if="column.key === 'create_time'">
        {{ formatTime(record.create_time) }}
      </template>
      <template v-else-if="column.key === 'update_time'">
        {{ formatTime(record.update_time) }}
      </template>
      <template v-else-if="column.key === 'action'">
        <div class="flex items-center justify-center gap-1">
          <Button
            size="small"
            title="编辑"
            type="text"
            @click="() => emit('edit', record)"
          >
            <template #icon>
              <Edit :size="14" />
            </template>
          </Button>
          <Popconfirm
            title="确定要删除这个作者吗？"
            ok-text="确定"
            cancel-text="取消"
            @confirm="() => emit('delete', record.id!)"
          >
            <Button
              danger
              size="small"
              title="删除"
              type="text"
            >
              <template #icon>
                <Trash2 :size="14" />
              </template>
            </Button>
          </Popconfirm>
        </div>
      </template>
    </template>
  </Table>
</template>
